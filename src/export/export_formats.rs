/// Export format definitions and options
/// 
/// This module defines the various export formats supported by the PDF editor
/// and their configuration options.

use serde::{Deserialize, Serialize};

/// Supported export formats
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum ExportFormat {
    /// Portable Document Format
    PDF,
    /// Portable Network Graphics
    PNG,
    /// Joint Photographic Experts Group
    JPEG,
    /// Scalable Vector Graphics
    SVG,
    /// HyperText Markup Language
    HTML,
}

impl ExportFormat {
    /// Get file extension for the format
    pub fn extension(&self) -> &'static str {
        match self {
            ExportFormat::PDF => "pdf",
            ExportFormat::PNG => "png",
            ExportFormat::JPEG => "jpg",
            ExportFormat::SVG => "svg",
            ExportFormat::HTML => "html",
        }
    }
    
    /// Get MIME type for the format
    pub fn mime_type(&self) -> &'static str {
        match self {
            ExportFormat::PDF => "application/pdf",
            ExportFormat::PNG => "image/png",
            ExportFormat::JPEG => "image/jpeg",
            ExportFormat::SVG => "image/svg+xml",
            ExportFormat::HTML => "text/html",
        }
    }
    
    /// Check if format supports multiple pages
    pub fn supports_multiple_pages(&self) -> bool {
        matches!(self, ExportFormat::PDF | ExportFormat::HTML)
    }
    
    /// Check if format is vector-based
    pub fn is_vector(&self) -> bool {
        matches!(self, ExportFormat::PDF | ExportFormat::SVG)
    }
    
    /// Check if format is raster-based
    pub fn is_raster(&self) -> bool {
        matches!(self, ExportFormat::PNG | ExportFormat::JPEG)
    }
    
    /// Get all supported formats
    pub fn all() -> Vec<ExportFormat> {
        vec![
            ExportFormat::PDF,
            ExportFormat::PNG,
            ExportFormat::JPEG,
            ExportFormat::SVG,
            ExportFormat::HTML,
        ]
    }
}

impl std::fmt::Display for ExportFormat {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ExportFormat::PDF => write!(f, "PDF"),
            ExportFormat::PNG => write!(f, "PNG"),
            ExportFormat::JPEG => write!(f, "JPEG"),
            ExportFormat::SVG => write!(f, "SVG"),
            ExportFormat::HTML => write!(f, "HTML"),
        }
    }
}

impl std::str::FromStr for ExportFormat {
    type Err = String;
    
    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s.to_lowercase().as_str() {
            "pdf" => Ok(ExportFormat::PDF),
            "png" => Ok(ExportFormat::PNG),
            "jpg" | "jpeg" => Ok(ExportFormat::JPEG),
            "svg" => Ok(ExportFormat::SVG),
            "html" | "htm" => Ok(ExportFormat::HTML),
            _ => Err(format!("Unsupported export format: {}", s)),
        }
    }
}

/// Export options configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExportOptions {
    /// PDF-specific options
    pub pdf_options: PDFExportOptions,
    /// Image-specific options
    pub image_options: ImageExportOptions,
    /// SVG-specific options
    pub svg_options: SVGExportOptions,
    /// HTML-specific options
    pub html_options: HTMLExportOptions,
    /// General options
    pub general_options: GeneralExportOptions,
}

impl Default for ExportOptions {
    fn default() -> Self {
        Self {
            pdf_options: PDFExportOptions::default(),
            image_options: ImageExportOptions::default(),
            svg_options: SVGExportOptions::default(),
            html_options: HTMLExportOptions::default(),
            general_options: GeneralExportOptions::default(),
        }
    }
}

/// PDF export options
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PDFExportOptions {
    /// PDF version to use
    pub pdf_version: PDFVersion,
    /// Compression level (0-9)
    pub compression_level: u8,
    /// Whether to embed fonts
    pub embed_fonts: bool,
    /// Whether to optimize for web viewing
    pub optimize_for_web: bool,
    /// Whether to include metadata
    pub include_metadata: bool,
    /// Password protection
    pub password: Option<String>,
    /// Permissions
    pub permissions: PDFPermissions,
}

impl Default for PDFExportOptions {
    fn default() -> Self {
        Self {
            pdf_version: PDFVersion::V1_7,
            compression_level: 6,
            embed_fonts: true,
            optimize_for_web: true,
            include_metadata: true,
            password: None,
            permissions: PDFPermissions::default(),
        }
    }
}

/// PDF version enumeration
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum PDFVersion {
    V1_4,
    V1_5,
    V1_6,
    V1_7,
    V2_0,
}

impl std::fmt::Display for PDFVersion {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            PDFVersion::V1_4 => write!(f, "1.4"),
            PDFVersion::V1_5 => write!(f, "1.5"),
            PDFVersion::V1_6 => write!(f, "1.6"),
            PDFVersion::V1_7 => write!(f, "1.7"),
            PDFVersion::V2_0 => write!(f, "2.0"),
        }
    }
}

/// PDF permissions
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PDFPermissions {
    /// Allow printing
    pub allow_print: bool,
    /// Allow copying text
    pub allow_copy: bool,
    /// Allow editing
    pub allow_edit: bool,
    /// Allow annotations
    pub allow_annotations: bool,
    /// Allow form filling
    pub allow_forms: bool,
}

impl Default for PDFPermissions {
    fn default() -> Self {
        Self {
            allow_print: true,
            allow_copy: true,
            allow_edit: true,
            allow_annotations: true,
            allow_forms: true,
        }
    }
}

/// Image export options
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ImageExportOptions {
    /// Image resolution in DPI
    pub dpi: u32,
    /// Image quality (0-100 for JPEG)
    pub quality: u8,
    /// Background color
    pub background_color: Option<(u8, u8, u8)>,
    /// Whether to include transparency (PNG only)
    pub include_transparency: bool,
    /// Whether to anti-alias
    pub anti_alias: bool,
}

impl Default for ImageExportOptions {
    fn default() -> Self {
        Self {
            dpi: 150,
            quality: 85,
            background_color: Some((255, 255, 255)), // White background
            include_transparency: false,
            anti_alias: true,
        }
    }
}

/// SVG export options
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SVGExportOptions {
    /// Whether to embed fonts
    pub embed_fonts: bool,
    /// Whether to convert text to paths
    pub text_to_paths: bool,
    /// Precision for coordinates
    pub coordinate_precision: u8,
    /// Whether to include metadata
    pub include_metadata: bool,
}

impl Default for SVGExportOptions {
    fn default() -> Self {
        Self {
            embed_fonts: true,
            text_to_paths: false,
            coordinate_precision: 2,
            include_metadata: true,
        }
    }
}

/// HTML export options
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HTMLExportOptions {
    /// Whether to include CSS styling
    pub include_css: bool,
    /// Whether to embed images
    pub embed_images: bool,
    /// Whether to preserve layout
    pub preserve_layout: bool,
    /// Whether to include navigation
    pub include_navigation: bool,
    /// CSS framework to use
    pub css_framework: Option<String>,
}

impl Default for HTMLExportOptions {
    fn default() -> Self {
        Self {
            include_css: true,
            embed_images: true,
            preserve_layout: true,
            include_navigation: true,
            css_framework: None,
        }
    }
}

/// General export options
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GeneralExportOptions {
    /// Whether to include progress callbacks
    pub show_progress: bool,
    /// Maximum file size in bytes (0 = no limit)
    pub max_file_size: usize,
    /// Whether to overwrite existing files
    pub overwrite_existing: bool,
    /// Custom metadata to include
    pub custom_metadata: std::collections::HashMap<String, String>,
}

impl Default for GeneralExportOptions {
    fn default() -> Self {
        Self {
            show_progress: true,
            max_file_size: 0, // No limit
            overwrite_existing: false,
            custom_metadata: std::collections::HashMap::new(),
        }
    }
}
