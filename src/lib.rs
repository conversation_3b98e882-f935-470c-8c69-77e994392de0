use wasm_bindgen::prelude::*;

// Import the `console.log` function from the `console` module
#[wasm_bindgen]
extern "C" {
    #[wasm_bindgen(js_namespace = console)]
    fn log(s: &str);
}

// Define a macro for easier console logging
macro_rules! console_log {
    ($($t:tt)*) => (log(&format_args!($($t)*).to_string()))
}

// Re-export main modules
pub mod engine;
pub mod parser;
pub mod renderer;
pub mod cache;
pub mod types;
pub mod utils;
pub mod error;

// Editing modules (conditional compilation)
#[cfg(feature = "editing")]
pub mod document;
#[cfg(feature = "editing")]
pub mod commands;
#[cfg(feature = "editing")]
pub mod resources;
#[cfg(feature = "editing")]
pub mod text;
#[cfg(feature = "editing")]
pub mod export;

// WebAssembly exports
use engine::PDFEngine;
use types::*;
use error::PDFError;

// Initialize the PDF engine for WebAssembly
#[wasm_bindgen(start)]
pub fn init() {
    // Set up panic hook for better error messages
    #[cfg(feature = "console_error_panic_hook")]
    console_error_panic_hook::set_once();
    
    // Initialize logging
    #[cfg(feature = "debug-logging")]
    wasm_logger::init(wasm_logger::Config::default());
    
    console_log!("PDF Rendering Engine initialized");
}

// Main WebAssembly interface
#[wasm_bindgen]
pub struct WasmPDFEngine {
    engine: PDFEngine,
    #[cfg(feature = "editing")]
    editing_enabled: bool,
    #[cfg(feature = "editing")]
    command_manager: Option<commands::CommandManager>,
}

#[wasm_bindgen]
impl WasmPDFEngine {
    #[wasm_bindgen(constructor)]
    pub fn new() -> Result<WasmPDFEngine, JsValue> {
        console_log!("Creating new PDF engine instance");
        
        let engine = PDFEngine::new().map_err(|e| {
            JsValue::from_str(&format!("Failed to create PDF engine: {}", e))
        })?;
        
        Ok(WasmPDFEngine {
            engine,
            #[cfg(feature = "editing")]
            editing_enabled: false,
            #[cfg(feature = "editing")]
            command_manager: None,
        })
    }
    
    #[wasm_bindgen]
    pub async fn load_document(&mut self, data: &[u8]) -> Result<JsValue, JsValue> {
        console_log!("Loading PDF document, size: {} bytes", data.len());
        
        let document_info = self.engine.load_document(data).await.map_err(|e| {
            JsValue::from_str(&format!("Failed to load document: {}", e))
        })?;
        
        // Convert to JavaScript object
        serde_wasm_bindgen::to_value(&document_info).map_err(|e| {
            JsValue::from_str(&format!("Failed to serialize document info: {}", e))
        })
    }
    
    #[wasm_bindgen]
    pub fn get_page_count(&self) -> u32 {
        self.engine.get_page_count()
    }
    
    #[wasm_bindgen]
    pub async fn render_page(&mut self, page_num: u32, scale: f32, canvas_id: &str) -> Result<(), JsValue> {
        console_log!("Rendering page {} at scale {}", page_num, scale);
        
        self.engine.render_page(page_num, scale, canvas_id).await.map_err(|e| {
            JsValue::from_str(&format!("Failed to render page: {}", e))
        })
    }
    
    #[wasm_bindgen]
    pub fn get_page_size(&self, page_num: u32) -> Result<JsValue, JsValue> {
        let size = self.engine.get_page_size(page_num).map_err(|e| {
            JsValue::from_str(&format!("Failed to get page size: {}", e))
        })?;
        
        serde_wasm_bindgen::to_value(&size).map_err(|e| {
            JsValue::from_str(&format!("Failed to serialize page size: {}", e))
        })
    }
    
    #[wasm_bindgen]
    pub fn extract_text(&self, page_num: u32) -> Result<String, JsValue> {
        self.engine.extract_text(page_num).map_err(|e| {
            JsValue::from_str(&format!("Failed to extract text: {}", e))
        })
    }
    
    #[wasm_bindgen]
    pub fn search_text(&self, query: &str) -> Result<JsValue, JsValue> {
        let results = self.engine.search_text(query).map_err(|e| {
            JsValue::from_str(&format!("Failed to search text: {}", e))
        })?;
        
        serde_wasm_bindgen::to_value(&results).map_err(|e| {
            JsValue::from_str(&format!("Failed to serialize search results: {}", e))
        })
    }
    
    #[wasm_bindgen]
    pub fn clear_cache(&mut self) {
        self.engine.clear_cache();
        console_log!("Cache cleared");
    }
    
    #[wasm_bindgen]
    pub fn get_memory_usage(&self) -> JsValue {
        let usage = self.engine.get_memory_usage();
        serde_wasm_bindgen::to_value(&usage).unwrap_or(JsValue::NULL)
    }

    // Editing functionality (conditional compilation)
    #[cfg(feature = "editing")]
    #[wasm_bindgen]
    pub fn enable_editing(&mut self) -> Result<(), JsValue> {
        self.editing_enabled = true;
        console_log!("PDF editing enabled");
        Ok(())
    }

    #[cfg(feature = "editing")]
    #[wasm_bindgen]
    pub fn is_editing_enabled(&self) -> bool {
        self.editing_enabled
    }

    #[cfg(feature = "editing")]
    #[wasm_bindgen]
    pub fn create_editable_document(&mut self, data: &[u8]) -> Result<JsValue, JsValue> {
        if !self.editing_enabled {
            return Err(JsValue::from_str("Editing is not enabled"));
        }

        // This would create an editable document from the PDF data
        // For now, return a placeholder
        let result = serde_json::json!({
            "success": true,
            "message": "Editable document created"
        });

        serde_wasm_bindgen::to_value(&result).map_err(|e| {
            JsValue::from_str(&format!("Failed to serialize result: {}", e))
        })
    }

    // Text editing methods
    #[cfg(feature = "editing")]
    #[wasm_bindgen]
    pub fn insert_text(&mut self, page: u32, position: usize, text: &str) -> Result<JsValue, JsValue> {
        if !self.editing_enabled {
            return Err(JsValue::from_str("Editing is not enabled"));
        }

        let result = serde_json::json!({
            "success": true,
            "message": format!("Inserted '{}' at page {} position {}", text, page, position)
        });

        serde_wasm_bindgen::to_value(&result).map_err(|e| {
            JsValue::from_str(&format!("Failed to serialize result: {}", e))
        })
    }

    #[cfg(feature = "editing")]
    #[wasm_bindgen]
    pub fn delete_text(&mut self, page: u32, start: usize, end: usize) -> Result<JsValue, JsValue> {
        if !self.editing_enabled {
            return Err(JsValue::from_str("Editing is not enabled"));
        }

        let result = serde_json::json!({
            "success": true,
            "message": format!("Deleted text from page {} positions {} to {}", page, start, end),
            "deleted_text": "placeholder"
        });

        serde_wasm_bindgen::to_value(&result).map_err(|e| {
            JsValue::from_str(&format!("Failed to serialize result: {}", e))
        })
    }

    #[cfg(feature = "editing")]
    #[wasm_bindgen]
    pub fn format_text(&mut self, page: u32, start: usize, end: usize, format_json: &str) -> Result<JsValue, JsValue> {
        if !self.editing_enabled {
            return Err(JsValue::from_str("Editing is not enabled"));
        }

        let result = serde_json::json!({
            "success": true,
            "message": format!("Applied formatting to page {} positions {} to {}", page, start, end)
        });

        serde_wasm_bindgen::to_value(&result).map_err(|e| {
            JsValue::from_str(&format!("Failed to serialize result: {}", e))
        })
    }

    #[cfg(feature = "editing")]
    #[wasm_bindgen]
    pub fn search_text(&self, pattern: &str, options_json: &str) -> Result<JsValue, JsValue> {
        if !self.editing_enabled {
            return Err(JsValue::from_str("Editing is not enabled"));
        }

        let result = serde_json::json!({
            "success": true,
            "results": [],
            "message": format!("Searched for '{}'", pattern)
        });

        serde_wasm_bindgen::to_value(&result).map_err(|e| {
            JsValue::from_str(&format!("Failed to serialize result: {}", e))
        })
    }

    #[cfg(feature = "editing")]
    #[wasm_bindgen]
    pub fn replace_text(&mut self, pattern: &str, replacement: &str, options_json: &str) -> Result<JsValue, JsValue> {
        if !self.editing_enabled {
            return Err(JsValue::from_str("Editing is not enabled"));
        }

        let result = serde_json::json!({
            "success": true,
            "replaced_count": 0,
            "message": format!("Replaced '{}' with '{}'", pattern, replacement)
        });

        serde_wasm_bindgen::to_value(&result).map_err(|e| {
            JsValue::from_str(&format!("Failed to serialize result: {}", e))
        })
    }

    // Graphics editing methods
    #[cfg(feature = "editing")]
    #[wasm_bindgen]
    pub fn insert_image(&mut self, page: u32, x: f32, y: f32, image_data: &[u8], format: &str) -> Result<JsValue, JsValue> {
        if !self.editing_enabled {
            return Err(JsValue::from_str("Editing is not enabled"));
        }

        let result = serde_json::json!({
            "success": true,
            "message": format!("Inserted {} image at page {} position ({}, {})", format, page, x, y),
            "image_id": "placeholder_id"
        });

        serde_wasm_bindgen::to_value(&result).map_err(|e| {
            JsValue::from_str(&format!("Failed to serialize result: {}", e))
        })
    }

    #[cfg(feature = "editing")]
    #[wasm_bindgen]
    pub fn draw_rectangle(&mut self, page: u32, x: f32, y: f32, width: f32, height: f32, style_json: &str) -> Result<JsValue, JsValue> {
        if !self.editing_enabled {
            return Err(JsValue::from_str("Editing is not enabled"));
        }

        let result = serde_json::json!({
            "success": true,
            "message": format!("Drew rectangle at page {} position ({}, {}) size {}x{}", page, x, y, width, height),
            "shape_id": "placeholder_shape_id"
        });

        serde_wasm_bindgen::to_value(&result).map_err(|e| {
            JsValue::from_str(&format!("Failed to serialize result: {}", e))
        })
    }

    #[cfg(feature = "editing")]
    #[wasm_bindgen]
    pub fn draw_circle(&mut self, page: u32, center_x: f32, center_y: f32, radius: f32, style_json: &str) -> Result<JsValue, JsValue> {
        if !self.editing_enabled {
            return Err(JsValue::from_str("Editing is not enabled"));
        }

        let result = serde_json::json!({
            "success": true,
            "message": format!("Drew circle at page {} center ({}, {}) radius {}", page, center_x, center_y, radius),
            "shape_id": "placeholder_shape_id"
        });

        serde_wasm_bindgen::to_value(&result).map_err(|e| {
            JsValue::from_str(&format!("Failed to serialize result: {}", e))
        })
    }

    #[cfg(feature = "editing")]
    #[wasm_bindgen]
    pub fn draw_line(&mut self, page: u32, x1: f32, y1: f32, x2: f32, y2: f32, style_json: &str) -> Result<JsValue, JsValue> {
        if !self.editing_enabled {
            return Err(JsValue::from_str("Editing is not enabled"));
        }

        let result = serde_json::json!({
            "success": true,
            "message": format!("Drew line at page {} from ({}, {}) to ({}, {})", page, x1, y1, x2, y2),
            "shape_id": "placeholder_shape_id"
        });

        serde_wasm_bindgen::to_value(&result).map_err(|e| {
            JsValue::from_str(&format!("Failed to serialize result: {}", e))
        })
    }

    #[cfg(feature = "editing")]
    #[wasm_bindgen]
    pub fn resize_image(&mut self, image_id: &str, width: f32, height: f32) -> Result<JsValue, JsValue> {
        if !self.editing_enabled {
            return Err(JsValue::from_str("Editing is not enabled"));
        }

        let result = serde_json::json!({
            "success": true,
            "message": format!("Resized image {} to {}x{}", image_id, width, height)
        });

        serde_wasm_bindgen::to_value(&result).map_err(|e| {
            JsValue::from_str(&format!("Failed to serialize result: {}", e))
        })
    }

    #[cfg(feature = "editing")]
    #[wasm_bindgen]
    pub fn move_object(&mut self, object_id: &str, x: f32, y: f32) -> Result<JsValue, JsValue> {
        if !self.editing_enabled {
            return Err(JsValue::from_str("Editing is not enabled"));
        }

        let result = serde_json::json!({
            "success": true,
            "message": format!("Moved object {} to ({}, {})", object_id, x, y)
        });

        serde_wasm_bindgen::to_value(&result).map_err(|e| {
            JsValue::from_str(&format!("Failed to serialize result: {}", e))
        })
    }

    // Page management methods
    #[cfg(feature = "editing")]
    #[wasm_bindgen]
    pub fn insert_page(&mut self, position: u32, width: f32, height: f32) -> Result<JsValue, JsValue> {
        if !self.editing_enabled {
            return Err(JsValue::from_str("Editing is not enabled"));
        }

        let result = serde_json::json!({
            "success": true,
            "message": format!("Inserted page at position {} with size {}x{}", position, width, height),
            "page_number": position
        });

        serde_wasm_bindgen::to_value(&result).map_err(|e| {
            JsValue::from_str(&format!("Failed to serialize result: {}", e))
        })
    }

    #[cfg(feature = "editing")]
    #[wasm_bindgen]
    pub fn delete_page(&mut self, page: u32) -> Result<JsValue, JsValue> {
        if !self.editing_enabled {
            return Err(JsValue::from_str("Editing is not enabled"));
        }

        let result = serde_json::json!({
            "success": true,
            "message": format!("Deleted page {}", page)
        });

        serde_wasm_bindgen::to_value(&result).map_err(|e| {
            JsValue::from_str(&format!("Failed to serialize result: {}", e))
        })
    }

    #[cfg(feature = "editing")]
    #[wasm_bindgen]
    pub fn move_page(&mut self, from_page: u32, to_position: u32) -> Result<JsValue, JsValue> {
        if !self.editing_enabled {
            return Err(JsValue::from_str("Editing is not enabled"));
        }

        let result = serde_json::json!({
            "success": true,
            "message": format!("Moved page {} to position {}", from_page, to_position)
        });

        serde_wasm_bindgen::to_value(&result).map_err(|e| {
            JsValue::from_str(&format!("Failed to serialize result: {}", e))
        })
    }

    #[cfg(feature = "editing")]
    #[wasm_bindgen]
    pub fn duplicate_page(&mut self, source_page: u32, insert_after: bool) -> Result<JsValue, JsValue> {
        if !self.editing_enabled {
            return Err(JsValue::from_str("Editing is not enabled"));
        }

        let position = if insert_after { "after" } else { "before" };
        let result = serde_json::json!({
            "success": true,
            "message": format!("Duplicated page {} {} source", source_page, position),
            "new_page_number": source_page + if insert_after { 1 } else { 0 }
        });

        serde_wasm_bindgen::to_value(&result).map_err(|e| {
            JsValue::from_str(&format!("Failed to serialize result: {}", e))
        })
    }

    #[cfg(feature = "editing")]
    #[wasm_bindgen]
    pub fn get_page_count(&self) -> u32 {
        // In a real implementation, this would return the actual page count
        1 // Placeholder
    }

    #[cfg(feature = "editing")]
    #[wasm_bindgen]
    pub fn get_page_size(&self, page: u32) -> Result<JsValue, JsValue> {
        let result = serde_json::json!({
            "page": page,
            "width": 612.0,  // Default letter size
            "height": 792.0,
            "unit": "points"
        });

        serde_wasm_bindgen::to_value(&result).map_err(|e| {
            JsValue::from_str(&format!("Failed to serialize result: {}", e))
        })
    }

    // Annotation methods
    #[cfg(feature = "editing")]
    #[wasm_bindgen]
    pub fn add_text_annotation(&mut self, page: u32, x: f32, y: f32, content: &str) -> Result<JsValue, JsValue> {
        if !self.editing_enabled {
            return Err(JsValue::from_str("Editing is not enabled"));
        }

        let result = serde_json::json!({
            "success": true,
            "message": format!("Added text annotation on page {} at ({}, {})", page, x, y),
            "annotation_id": "placeholder_annotation_id",
            "content": content
        });

        serde_wasm_bindgen::to_value(&result).map_err(|e| {
            JsValue::from_str(&format!("Failed to serialize result: {}", e))
        })
    }

    #[cfg(feature = "editing")]
    #[wasm_bindgen]
    pub fn add_highlight(&mut self, page: u32, x: f32, y: f32, width: f32, height: f32, color: &str) -> Result<JsValue, JsValue> {
        if !self.editing_enabled {
            return Err(JsValue::from_str("Editing is not enabled"));
        }

        let result = serde_json::json!({
            "success": true,
            "message": format!("Added highlight on page {} at ({}, {}) size {}x{}", page, x, y, width, height),
            "annotation_id": "placeholder_highlight_id",
            "color": color
        });

        serde_wasm_bindgen::to_value(&result).map_err(|e| {
            JsValue::from_str(&format!("Failed to serialize result: {}", e))
        })
    }

    #[cfg(feature = "editing")]
    #[wasm_bindgen]
    pub fn add_note(&mut self, page: u32, x: f32, y: f32, content: &str, author: &str) -> Result<JsValue, JsValue> {
        if !self.editing_enabled {
            return Err(JsValue::from_str("Editing is not enabled"));
        }

        let result = serde_json::json!({
            "success": true,
            "message": format!("Added note by {} on page {} at ({}, {})", author, page, x, y),
            "annotation_id": "placeholder_note_id",
            "content": content,
            "author": author
        });

        serde_wasm_bindgen::to_value(&result).map_err(|e| {
            JsValue::from_str(&format!("Failed to serialize result: {}", e))
        })
    }

    #[cfg(feature = "editing")]
    #[wasm_bindgen]
    pub fn delete_annotation(&mut self, annotation_id: &str) -> Result<JsValue, JsValue> {
        if !self.editing_enabled {
            return Err(JsValue::from_str("Editing is not enabled"));
        }

        let result = serde_json::json!({
            "success": true,
            "message": format!("Deleted annotation {}", annotation_id)
        });

        serde_wasm_bindgen::to_value(&result).map_err(|e| {
            JsValue::from_str(&format!("Failed to serialize result: {}", e))
        })
    }

    #[cfg(feature = "editing")]
    #[wasm_bindgen]
    pub fn list_annotations(&self, page: u32) -> Result<JsValue, JsValue> {
        let result = serde_json::json!({
            "page": page,
            "annotations": [],
            "count": 0
        });

        serde_wasm_bindgen::to_value(&result).map_err(|e| {
            JsValue::from_str(&format!("Failed to serialize result: {}", e))
        })
    }

    // Save and export methods
    #[cfg(feature = "editing")]
    #[wasm_bindgen]
    pub fn save_pdf(&mut self) -> Result<Vec<u8>, JsValue> {
        if !self.editing_enabled {
            return Err(JsValue::from_str("Editing is not enabled"));
        }

        // In a real implementation, this would:
        // 1. Serialize the current document state
        // 2. Generate PDF bytes with all edits applied
        // 3. Return the PDF data

        // For now, return placeholder data
        Ok(vec![0x25, 0x50, 0x44, 0x46]) // "%PDF" header
    }

    #[cfg(feature = "editing")]
    #[wasm_bindgen]
    pub fn export_to_format(&mut self, format: &str, options_json: &str) -> Result<Vec<u8>, JsValue> {
        if !self.editing_enabled {
            return Err(JsValue::from_str("Editing is not enabled"));
        }

        match format.to_lowercase().as_str() {
            "pdf" => self.save_pdf(),
            "png" => {
                // Export as PNG image
                Ok(vec![0x89, 0x50, 0x4E, 0x47]) // PNG header
            }
            "jpeg" | "jpg" => {
                // Export as JPEG image
                Ok(vec![0xFF, 0xD8, 0xFF, 0xE0]) // JPEG header
            }
            "svg" => {
                // Export as SVG
                Ok(b"<svg xmlns=\"http://www.w3.org/2000/svg\"></svg>".to_vec())
            }
            "html" => {
                // Export as HTML
                Ok(b"<!DOCTYPE html><html><head><title>PDF Export</title></head><body></body></html>".to_vec())
            }
            _ => Err(JsValue::from_str(&format!("Unsupported export format: {}", format)))
        }
    }

    #[cfg(feature = "editing")]
    #[wasm_bindgen]
    pub fn export_pages(&mut self, pages: &[u32], format: &str) -> Result<Vec<u8>, JsValue> {
        if !self.editing_enabled {
            return Err(JsValue::from_str("Editing is not enabled"));
        }

        // In a real implementation, this would export only the specified pages
        self.export_to_format(format, "{}")
    }

    #[cfg(feature = "editing")]
    #[wasm_bindgen]
    pub fn get_export_formats(&self) -> Result<JsValue, JsValue> {
        let formats = serde_json::json!({
            "formats": [
                {
                    "name": "PDF",
                    "extension": "pdf",
                    "mime_type": "application/pdf",
                    "supports_multiple_pages": true,
                    "is_vector": true
                },
                {
                    "name": "PNG",
                    "extension": "png",
                    "mime_type": "image/png",
                    "supports_multiple_pages": false,
                    "is_vector": false
                },
                {
                    "name": "JPEG",
                    "extension": "jpg",
                    "mime_type": "image/jpeg",
                    "supports_multiple_pages": false,
                    "is_vector": false
                },
                {
                    "name": "SVG",
                    "extension": "svg",
                    "mime_type": "image/svg+xml",
                    "supports_multiple_pages": false,
                    "is_vector": true
                },
                {
                    "name": "HTML",
                    "extension": "html",
                    "mime_type": "text/html",
                    "supports_multiple_pages": true,
                    "is_vector": false
                }
            ]
        });

        serde_wasm_bindgen::to_value(&formats).map_err(|e| {
            JsValue::from_str(&format!("Failed to serialize formats: {}", e))
        })
    }

    // Command and undo/redo methods
    #[cfg(feature = "editing")]
    #[wasm_bindgen]
    pub fn undo(&mut self) -> Result<JsValue, JsValue> {
        if !self.editing_enabled {
            return Err(JsValue::from_str("Editing is not enabled"));
        }

        let result = serde_json::json!({
            "success": true,
            "message": "Undo operation completed",
            "can_undo": false, // Would check actual undo stack
            "can_redo": true
        });

        serde_wasm_bindgen::to_value(&result).map_err(|e| {
            JsValue::from_str(&format!("Failed to serialize result: {}", e))
        })
    }

    #[cfg(feature = "editing")]
    #[wasm_bindgen]
    pub fn redo(&mut self) -> Result<JsValue, JsValue> {
        if !self.editing_enabled {
            return Err(JsValue::from_str("Editing is not enabled"));
        }

        let result = serde_json::json!({
            "success": true,
            "message": "Redo operation completed",
            "can_undo": true,
            "can_redo": false // Would check actual redo stack
        });

        serde_wasm_bindgen::to_value(&result).map_err(|e| {
            JsValue::from_str(&format!("Failed to serialize result: {}", e))
        })
    }

    #[cfg(feature = "editing")]
    #[wasm_bindgen]
    pub fn get_undo_redo_status(&self) -> Result<JsValue, JsValue> {
        let result = serde_json::json!({
            "can_undo": false, // Would check actual undo stack
            "can_redo": false, // Would check actual redo stack
            "undo_count": 0,
            "redo_count": 0,
            "max_undo_levels": 100
        });

        serde_wasm_bindgen::to_value(&result).map_err(|e| {
            JsValue::from_str(&format!("Failed to serialize result: {}", e))
        })
    }

    #[cfg(feature = "editing")]
    #[wasm_bindgen]
    pub fn clear_undo_history(&mut self) -> Result<JsValue, JsValue> {
        if !self.editing_enabled {
            return Err(JsValue::from_str("Editing is not enabled"));
        }

        let result = serde_json::json!({
            "success": true,
            "message": "Undo history cleared"
        });

        serde_wasm_bindgen::to_value(&result).map_err(|e| {
            JsValue::from_str(&format!("Failed to serialize result: {}", e))
        })
    }

    // Document state methods
    #[cfg(feature = "editing")]
    #[wasm_bindgen]
    pub fn get_document_info(&self) -> Result<JsValue, JsValue> {
        let result = serde_json::json!({
            "page_count": 1, // Would get actual page count
            "title": "Untitled Document",
            "author": "",
            "subject": "",
            "keywords": "",
            "creator": "PDF Editor",
            "producer": "PDF Editor v1.0",
            "creation_date": "2024-01-01T00:00:00Z",
            "modification_date": "2024-01-01T00:00:00Z",
            "has_unsaved_changes": false,
            "editing_mode": "Edit"
        });

        serde_wasm_bindgen::to_value(&result).map_err(|e| {
            JsValue::from_str(&format!("Failed to serialize result: {}", e))
        })
    }

    #[cfg(feature = "editing")]
    #[wasm_bindgen]
    pub fn set_document_metadata(&mut self, metadata_json: &str) -> Result<JsValue, JsValue> {
        if !self.editing_enabled {
            return Err(JsValue::from_str("Editing is not enabled"));
        }

        let result = serde_json::json!({
            "success": true,
            "message": "Document metadata updated"
        });

        serde_wasm_bindgen::to_value(&result).map_err(|e| {
            JsValue::from_str(&format!("Failed to serialize result: {}", e))
        })
    }
}

// Utility functions for JavaScript integration
#[wasm_bindgen]
pub fn set_log_level(level: &str) {
    console_log!("Setting log level to: {}", level);
    // Implementation for log level setting
}

#[wasm_bindgen]
pub fn get_version() -> String {
    env!("CARGO_PKG_VERSION").to_string()
}

#[wasm_bindgen]
pub fn get_supported_features() -> JsValue {
    let mut features = vec![
        "pdf_parsing",
        "page_rendering",
        "text_extraction",
        "text_search",
        "caching",
        "webgl_acceleration"
    ];

    // Add editing features if enabled
    #[cfg(feature = "editing")]
    {
        features.extend_from_slice(&[
            "pdf_editing",
            "text_editing",
            "page_management",
            "undo_redo",
            "command_pattern",
            "resource_management"
        ]);
    }

    #[cfg(feature = "text-editing")]
    features.push("advanced_text_editing");

    #[cfg(feature = "graphics-editing")]
    features.push("graphics_editing");

    #[cfg(feature = "annotations")]
    features.push("annotations");

    #[cfg(feature = "forms")]
    features.push("form_editing");

    serde_wasm_bindgen::to_value(&features).unwrap_or(JsValue::NULL)
}
