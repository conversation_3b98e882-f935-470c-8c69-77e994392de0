/// Color management system
/// 
/// This module provides comprehensive color management including
/// color spaces, profiles, and color conversion.

use super::*;
use crate::document::Color;
use crate::error::{PDFError, PDFResult};

use std::collections::HashMap;
use serde::{Deserialize, Serialize};

/// Color space enumeration
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq, Serialize, Deserialize)]
pub enum ColorSpace {
    RGB,
    CMYK,
    Grayscale,
    Lab,
    XYZ,
    HSV,
    HSL,
}

impl ColorSpace {
    /// Get the number of components for this color space
    pub fn component_count(&self) -> usize {
        match self {
            ColorSpace::RGB => 3,
            ColorSpace::CMYK => 4,
            ColorSpace::Grayscale => 1,
            ColorSpace::Lab => 3,
            ColorSpace::XYZ => 3,
            ColorSpace::HSV => 3,
            ColorSpace::HSL => 3,
        }
    }
    
    /// Get component names for this color space
    pub fn component_names(&self) -> Vec<&'static str> {
        match self {
            ColorSpace::RGB => vec!["<PERSON>", "<PERSON>", "<PERSON>"],
            ColorSpace::CMYK => vec!["<PERSON><PERSON>", "<PERSON>gent<PERSON>", "<PERSON>", "<PERSON>"],
            <PERSON>Space::Grayscale => vec!["Gray"],
            ColorSpace::Lab => vec!["L*", "a*", "b*"],
            ColorSpace::XYZ => vec!["X", "Y", "Z"],
            ColorSpace::HSV => vec!["Hue", "Saturation", "Value"],
            ColorSpace::HSL => vec!["Hue", "Saturation", "Lightness"],
        }
    }
}

/// Color manager for handling color resources and conversions
#[derive(Debug)]
pub struct ColorManager {
    /// Named colors
    named_colors: HashMap<String, Color>,
    /// Color profiles
    profiles: HashMap<String, ColorProfile>,
    /// Color palettes
    palettes: HashMap<String, ColorPalette>,
    /// Default color space
    default_color_space: ColorSpace,
    /// Color conversion cache
    conversion_cache: HashMap<String, Color>,
}

impl ColorManager {
    /// Create a new color manager
    pub fn new() -> Self {
        let mut manager = Self {
            named_colors: HashMap::new(),
            profiles: HashMap::new(),
            palettes: HashMap::new(),
            default_color_space: ColorSpace::RGB,
            conversion_cache: HashMap::new(),
        };
        
        // Initialize with standard colors
        manager.initialize_standard_colors();
        manager.initialize_standard_profiles();
        
        manager
    }
    
    /// Add a named color
    pub fn add_named_color(&mut self, name: String, color: Color) {
        self.named_colors.insert(name, color);
    }
    
    /// Get a named color
    pub fn get_named_color(&self, name: &str) -> Option<&Color> {
        self.named_colors.get(name)
    }
    
    /// Convert color between color spaces
    pub fn convert_color(&mut self, color: &Color, from: ColorSpace, to: ColorSpace) -> PDFResult<Color> {
        if from == to {
            return Ok(*color);
        }
        
        let cache_key = format!("{:?}_{:?}_{:.3}_{:.3}_{:.3}_{:.3}", 
            from, to, color.r, color.g, color.b, color.a);
        
        if let Some(cached) = self.conversion_cache.get(&cache_key) {
            return Ok(*cached);
        }
        
        let converted = match (from, to) {
            (ColorSpace::RGB, ColorSpace::CMYK) => self.rgb_to_cmyk(color),
            (ColorSpace::CMYK, ColorSpace::RGB) => self.cmyk_to_rgb(color),
            (ColorSpace::RGB, ColorSpace::Grayscale) => self.rgb_to_grayscale(color),
            (ColorSpace::Grayscale, ColorSpace::RGB) => self.grayscale_to_rgb(color),
            (ColorSpace::RGB, ColorSpace::HSV) => self.rgb_to_hsv(color),
            (ColorSpace::HSV, ColorSpace::RGB) => self.hsv_to_rgb(color),
            (ColorSpace::RGB, ColorSpace::HSL) => self.rgb_to_hsl(color),
            (ColorSpace::HSL, ColorSpace::RGB) => self.hsl_to_rgb(color),
            _ => {
                // For unsupported conversions, convert through RGB
                if from != ColorSpace::RGB {
                    let rgb = self.convert_color(color, from, ColorSpace::RGB)?;
                    self.convert_color(&rgb, ColorSpace::RGB, to)?
                } else {
                    *color // Fallback
                }
            }
        };
        
        self.conversion_cache.insert(cache_key, converted);
        Ok(converted)
    }
    
    /// Create a color palette
    pub fn create_palette(&mut self, name: String, colors: Vec<Color>) -> PDFResult<()> {
        let palette = ColorPalette {
            name: name.clone(),
            colors,
            description: None,
        };
        
        self.palettes.insert(name, palette);
        Ok(())
    }
    
    /// Get a color palette
    pub fn get_palette(&self, name: &str) -> Option<&ColorPalette> {
        self.palettes.get(name)
    }
    
    /// List available palettes
    pub fn list_palettes(&self) -> Vec<&String> {
        self.palettes.keys().collect()
    }
    
    /// Add a color profile
    pub fn add_profile(&mut self, name: String, profile: ColorProfile) {
        self.profiles.insert(name, profile);
    }
    
    /// Get a color profile
    pub fn get_profile(&self, name: &str) -> Option<&ColorProfile> {
        self.profiles.get(name)
    }
    
    /// Set default color space
    pub fn set_default_color_space(&mut self, color_space: ColorSpace) {
        self.default_color_space = color_space;
    }
    
    /// Get default color space
    pub fn default_color_space(&self) -> ColorSpace {
        self.default_color_space
    }
    
    /// Clear conversion cache
    pub fn clear_cache(&mut self) {
        self.conversion_cache.clear();
    }
    
    /// Get memory usage
    pub fn memory_usage(&self) -> usize {
        let mut size = 0;
        
        // Named colors
        size += self.named_colors.len() * (std::mem::size_of::<String>() + std::mem::size_of::<Color>());
        
        // Palettes
        for palette in self.palettes.values() {
            size += palette.colors.len() * std::mem::size_of::<Color>();
        }
        
        // Conversion cache
        size += self.conversion_cache.len() * (std::mem::size_of::<String>() + std::mem::size_of::<Color>());
        
        size
    }
    
    /// Optimize color usage
    pub fn optimize(&mut self) -> PDFResult<super::OptimizationResult> {
        let mut result = super::OptimizationResult::default();
        
        // Clear conversion cache
        let cache_size = self.conversion_cache.len();
        self.conversion_cache.clear();
        result.memory_saved += cache_size * (std::mem::size_of::<String>() + std::mem::size_of::<Color>());
        result.colors_optimized = cache_size as u32;
        
        Ok(result)
    }
    
    /// Validate colors
    pub fn validate(&self) -> super::ResourceValidationResult {
        let mut result = super::ResourceValidationResult {
            is_valid: true,
            errors: Vec::new(),
            warnings: Vec::new(),
        };
        
        // Validate named colors
        for (name, color) in &self.named_colors {
            if color.r < 0.0 || color.r > 1.0 ||
               color.g < 0.0 || color.g > 1.0 ||
               color.b < 0.0 || color.b > 1.0 ||
               color.a < 0.0 || color.a > 1.0 {
                result.errors.push(format!("Color '{}' has invalid component values", name));
                result.is_valid = false;
            }
        }
        
        // Check for large conversion cache
        if self.conversion_cache.len() > 1000 {
            result.warnings.push("Color conversion cache is very large".to_string());
        }
        
        result
    }
    
    /// Initialize standard colors
    fn initialize_standard_colors(&mut self) {
        let standard_colors = [
            ("black", Color::black()),
            ("white", Color::white()),
            ("red", Color::red()),
            ("green", Color::green()),
            ("blue", Color::blue()),
            ("yellow", Color::rgb(1.0, 1.0, 0.0)),
            ("cyan", Color::rgb(0.0, 1.0, 1.0)),
            ("magenta", Color::rgb(1.0, 0.0, 1.0)),
            ("gray", Color::rgb(0.5, 0.5, 0.5)),
            ("darkgray", Color::rgb(0.25, 0.25, 0.25)),
            ("lightgray", Color::rgb(0.75, 0.75, 0.75)),
        ];
        
        for (name, color) in &standard_colors {
            self.named_colors.insert(name.to_string(), *color);
        }
    }
    
    /// Initialize standard color profiles
    fn initialize_standard_profiles(&mut self) {
        // sRGB profile
        self.profiles.insert("sRGB".to_string(), ColorProfile {
            name: "sRGB".to_string(),
            color_space: ColorSpace::RGB,
            description: Some("Standard RGB color space".to_string()),
            data: None,
        });
        
        // Adobe RGB profile
        self.profiles.insert("AdobeRGB".to_string(), ColorProfile {
            name: "Adobe RGB".to_string(),
            color_space: ColorSpace::RGB,
            description: Some("Adobe RGB color space".to_string()),
            data: None,
        });
    }
    
    // Color conversion methods
    fn rgb_to_cmyk(&self, color: &Color) -> Color {
        let k = 1.0 - color.r.max(color.g).max(color.b);
        let c = if k < 1.0 { (1.0 - color.r - k) / (1.0 - k) } else { 0.0 };
        let m = if k < 1.0 { (1.0 - color.g - k) / (1.0 - k) } else { 0.0 };
        let y = if k < 1.0 { (1.0 - color.b - k) / (1.0 - k) } else { 0.0 };
        
        Color::new(c, m, y, k)
    }
    
    fn cmyk_to_rgb(&self, color: &Color) -> Color {
        let r = (1.0 - color.r) * (1.0 - color.a);
        let g = (1.0 - color.g) * (1.0 - color.a);
        let b = (1.0 - color.b) * (1.0 - color.a);
        
        Color::rgb(r, g, b)
    }
    
    fn rgb_to_grayscale(&self, color: &Color) -> Color {
        let gray = 0.299 * color.r + 0.587 * color.g + 0.114 * color.b;
        Color::rgb(gray, gray, gray)
    }
    
    fn grayscale_to_rgb(&self, color: &Color) -> Color {
        Color::rgb(color.r, color.r, color.r)
    }
    
    fn rgb_to_hsv(&self, color: &Color) -> Color {
        let max = color.r.max(color.g).max(color.b);
        let min = color.r.min(color.g).min(color.b);
        let delta = max - min;
        
        let h = if delta == 0.0 {
            0.0
        } else if max == color.r {
            60.0 * (((color.g - color.b) / delta) % 6.0)
        } else if max == color.g {
            60.0 * (((color.b - color.r) / delta) + 2.0)
        } else {
            60.0 * (((color.r - color.g) / delta) + 4.0)
        };
        
        let s = if max == 0.0 { 0.0 } else { delta / max };
        let v = max;
        
        Color::new(h / 360.0, s, v, color.a)
    }
    
    fn hsv_to_rgb(&self, color: &Color) -> Color {
        let h = color.r * 360.0;
        let s = color.g;
        let v = color.b;
        
        let c = v * s;
        let x = c * (1.0 - ((h / 60.0) % 2.0 - 1.0).abs());
        let m = v - c;
        
        let (r, g, b) = if h < 60.0 {
            (c, x, 0.0)
        } else if h < 120.0 {
            (x, c, 0.0)
        } else if h < 180.0 {
            (0.0, c, x)
        } else if h < 240.0 {
            (0.0, x, c)
        } else if h < 300.0 {
            (x, 0.0, c)
        } else {
            (c, 0.0, x)
        };
        
        Color::new(r + m, g + m, b + m, color.a)
    }
    
    fn rgb_to_hsl(&self, color: &Color) -> Color {
        let max = color.r.max(color.g).max(color.b);
        let min = color.r.min(color.g).min(color.b);
        let delta = max - min;
        
        let l = (max + min) / 2.0;
        
        let s = if delta == 0.0 {
            0.0
        } else if l < 0.5 {
            delta / (max + min)
        } else {
            delta / (2.0 - max - min)
        };
        
        let h = if delta == 0.0 {
            0.0
        } else if max == color.r {
            60.0 * (((color.g - color.b) / delta) % 6.0)
        } else if max == color.g {
            60.0 * (((color.b - color.r) / delta) + 2.0)
        } else {
            60.0 * (((color.r - color.g) / delta) + 4.0)
        };
        
        Color::new(h / 360.0, s, l, color.a)
    }
    
    fn hsl_to_rgb(&self, color: &Color) -> Color {
        let h = color.r * 360.0;
        let s = color.g;
        let l = color.b;
        
        let c = (1.0 - (2.0 * l - 1.0).abs()) * s;
        let x = c * (1.0 - ((h / 60.0) % 2.0 - 1.0).abs());
        let m = l - c / 2.0;
        
        let (r, g, b) = if h < 60.0 {
            (c, x, 0.0)
        } else if h < 120.0 {
            (x, c, 0.0)
        } else if h < 180.0 {
            (0.0, c, x)
        } else if h < 240.0 {
            (0.0, x, c)
        } else if h < 300.0 {
            (x, 0.0, c)
        } else {
            (c, 0.0, x)
        };
        
        Color::new(r + m, g + m, b + m, color.a)
    }
}

/// Color profile information
#[derive(Debug, Clone)]
pub struct ColorProfile {
    pub name: String,
    pub color_space: ColorSpace,
    pub description: Option<String>,
    pub data: Option<Vec<u8>>,
}

/// Color palette
#[derive(Debug, Clone)]
pub struct ColorPalette {
    pub name: String,
    pub colors: Vec<Color>,
    pub description: Option<String>,
}
