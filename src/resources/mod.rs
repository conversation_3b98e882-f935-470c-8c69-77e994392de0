/// Resource management system
/// 
/// This module provides comprehensive resource management for fonts,
/// images, colors, and other assets used in PDF editing.

pub mod font_manager;
pub mod image_manager;
pub mod color_manager;

pub use font_manager::{FontManager, FontResource, FontMetrics};
pub use image_manager::{ImageManager, ImageResource, ImageFormat};
pub use color_manager::{ColorManager, ColorSpace, ColorProfile};

use crate::document::Color;
use crate::error::{PDFError, PDFResult};

use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use serde::{Deserialize, Serialize};

/// Central resource manager
#[derive(Debug)]
pub struct ResourceManager {
    /// Font management
    font_manager: FontManager,
    /// Image management
    image_manager: ImageManager,
    /// Color management
    color_manager: ColorManager,
    /// Resource cache
    cache: ResourceCache,
    /// Resource usage statistics
    stats: ResourceStats,
}

impl ResourceManager {
    /// Create a new resource manager
    pub fn new() -> Self {
        Self {
            font_manager: FontManager::new(),
            image_manager: ImageManager::new(),
            color_manager: ColorManager::new(),
            cache: ResourceCache::new(),
            stats: ResourceStats::default(),
        }
    }
    
    /// Get the font manager
    pub fn font_manager(&self) -> &FontManager {
        &self.font_manager
    }
    
    /// Get mutable font manager
    pub fn font_manager_mut(&mut self) -> &mut FontManager {
        &mut self.font_manager
    }
    
    /// Get the image manager
    pub fn image_manager(&self) -> &ImageManager {
        &self.image_manager
    }
    
    /// Get mutable image manager
    pub fn image_manager_mut(&mut self) -> &mut ImageManager {
        &mut self.image_manager
    }
    
    /// Get the color manager
    pub fn color_manager(&self) -> &ColorManager {
        &self.color_manager
    }
    
    /// Get mutable color manager
    pub fn color_manager_mut(&mut self) -> &mut ColorManager {
        &mut self.color_manager
    }
    
    /// Get resource usage statistics
    pub fn stats(&self) -> &ResourceStats {
        &self.stats
    }
    
    /// Clear all cached resources
    pub fn clear_cache(&mut self) {
        self.cache.clear();
        self.font_manager.clear_cache();
        self.image_manager.clear_cache();
        self.color_manager.clear_cache();
    }
    
    /// Get memory usage of all resources
    pub fn memory_usage(&self) -> ResourceMemoryUsage {
        ResourceMemoryUsage {
            fonts: self.font_manager.memory_usage(),
            images: self.image_manager.memory_usage(),
            colors: self.color_manager.memory_usage(),
            cache: self.cache.memory_usage(),
            total: self.font_manager.memory_usage() + 
                   self.image_manager.memory_usage() + 
                   self.color_manager.memory_usage() + 
                   self.cache.memory_usage(),
        }
    }
    
    /// Optimize resource usage
    pub fn optimize(&mut self) -> PDFResult<OptimizationResult> {
        let mut result = OptimizationResult::default();
        
        // Optimize fonts
        let font_optimization = self.font_manager.optimize()?;
        result.fonts_optimized = font_optimization.items_optimized;
        result.memory_saved += font_optimization.memory_saved;
        
        // Optimize images
        let image_optimization = self.image_manager.optimize()?;
        result.images_optimized = image_optimization.items_optimized;
        result.memory_saved += image_optimization.memory_saved;
        
        // Optimize colors
        let color_optimization = self.color_manager.optimize()?;
        result.colors_optimized = color_optimization.items_optimized;
        result.memory_saved += color_optimization.memory_saved;
        
        // Update statistics
        self.stats.optimizations_performed += 1;
        self.stats.total_memory_saved += result.memory_saved;
        
        Ok(result)
    }
    
    /// Validate all resources
    pub fn validate(&self) -> ResourceValidationResult {
        let mut result = ResourceValidationResult {
            is_valid: true,
            errors: Vec::new(),
            warnings: Vec::new(),
        };
        
        // Validate fonts
        let font_validation = self.font_manager.validate();
        result.errors.extend(font_validation.errors);
        result.warnings.extend(font_validation.warnings);
        if !font_validation.is_valid {
            result.is_valid = false;
        }
        
        // Validate images
        let image_validation = self.image_manager.validate();
        result.errors.extend(image_validation.errors);
        result.warnings.extend(image_validation.warnings);
        if !image_validation.is_valid {
            result.is_valid = false;
        }
        
        // Validate colors
        let color_validation = self.color_manager.validate();
        result.errors.extend(color_validation.errors);
        result.warnings.extend(color_validation.warnings);
        if !color_validation.is_valid {
            result.is_valid = false;
        }
        
        result
    }
}

/// Resource cache for frequently used resources
#[derive(Debug)]
pub struct ResourceCache {
    /// Cached font data
    fonts: HashMap<String, Arc<Vec<u8>>>,
    /// Cached image data
    images: HashMap<String, Arc<Vec<u8>>>,
    /// Cache statistics
    hits: u64,
    misses: u64,
    /// Maximum cache size in bytes
    max_size: usize,
    /// Current cache size in bytes
    current_size: usize,
}

impl ResourceCache {
    pub fn new() -> Self {
        Self {
            fonts: HashMap::new(),
            images: HashMap::new(),
            hits: 0,
            misses: 0,
            max_size: 50 * 1024 * 1024, // 50MB default
            current_size: 0,
        }
    }
    
    /// Get cached font data
    pub fn get_font(&mut self, key: &str) -> Option<Arc<Vec<u8>>> {
        if let Some(data) = self.fonts.get(key) {
            self.hits += 1;
            Some(data.clone())
        } else {
            self.misses += 1;
            None
        }
    }
    
    /// Cache font data
    pub fn cache_font(&mut self, key: String, data: Vec<u8>) -> PDFResult<()> {
        let data_size = data.len();
        
        // Check if we have space
        if self.current_size + data_size > self.max_size {
            self.evict_lru()?;
        }
        
        self.fonts.insert(key, Arc::new(data));
        self.current_size += data_size;
        
        Ok(())
    }
    
    /// Get cached image data
    pub fn get_image(&mut self, key: &str) -> Option<Arc<Vec<u8>>> {
        if let Some(data) = self.images.get(key) {
            self.hits += 1;
            Some(data.clone())
        } else {
            self.misses += 1;
            None
        }
    }
    
    /// Cache image data
    pub fn cache_image(&mut self, key: String, data: Vec<u8>) -> PDFResult<()> {
        let data_size = data.len();
        
        // Check if we have space
        if self.current_size + data_size > self.max_size {
            self.evict_lru()?;
        }
        
        self.images.insert(key, Arc::new(data));
        self.current_size += data_size;
        
        Ok(())
    }
    
    /// Clear all cached data
    pub fn clear(&mut self) {
        self.fonts.clear();
        self.images.clear();
        self.current_size = 0;
    }
    
    /// Get cache hit rate
    pub fn hit_rate(&self) -> f64 {
        let total = self.hits + self.misses;
        if total == 0 {
            0.0
        } else {
            self.hits as f64 / total as f64
        }
    }
    
    /// Get memory usage
    pub fn memory_usage(&self) -> usize {
        self.current_size
    }
    
    /// Evict least recently used items
    fn evict_lru(&mut self) -> PDFResult<()> {
        // Simple eviction: remove half the cache
        // In a real implementation, this would use proper LRU tracking
        let target_size = self.max_size / 2;
        
        while self.current_size > target_size && (!self.fonts.is_empty() || !self.images.is_empty()) {
            if !self.fonts.is_empty() {
                if let Some((key, _)) = self.fonts.iter().next() {
                    let key = key.clone();
                    self.fonts.remove(&key);
                }
            }
            
            if !self.images.is_empty() {
                if let Some((key, _)) = self.images.iter().next() {
                    let key = key.clone();
                    self.images.remove(&key);
                }
            }
            
            // Recalculate size (simplified)
            self.current_size = self.fonts.len() * 1000 + self.images.len() * 5000; // Rough estimate
        }
        
        Ok(())
    }
}

/// Resource usage statistics
#[derive(Debug, Default)]
pub struct ResourceStats {
    /// Number of fonts loaded
    pub fonts_loaded: u32,
    /// Number of images loaded
    pub images_loaded: u32,
    /// Number of colors defined
    pub colors_defined: u32,
    /// Number of optimizations performed
    pub optimizations_performed: u32,
    /// Total memory saved through optimization
    pub total_memory_saved: usize,
    /// Cache hit rate
    pub cache_hit_rate: f64,
}

/// Memory usage breakdown
#[derive(Debug, Clone)]
pub struct ResourceMemoryUsage {
    pub fonts: usize,
    pub images: usize,
    pub colors: usize,
    pub cache: usize,
    pub total: usize,
}

/// Optimization result
#[derive(Debug, Default)]
pub struct OptimizationResult {
    pub fonts_optimized: u32,
    pub images_optimized: u32,
    pub colors_optimized: u32,
    pub memory_saved: usize,
}

/// Resource validation result
#[derive(Debug)]
pub struct ResourceValidationResult {
    pub is_valid: bool,
    pub errors: Vec<String>,
    pub warnings: Vec<String>,
}

/// Resource type enumeration
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ResourceType {
    Font,
    Image,
    Color,
    Pattern,
    Gradient,
}

/// Resource reference for tracking usage
#[derive(Debug, Clone)]
pub struct ResourceReference {
    pub resource_type: ResourceType,
    pub id: String,
    pub name: String,
    pub usage_count: u32,
    pub memory_size: usize,
}

impl ResourceReference {
    pub fn new(resource_type: ResourceType, id: String, name: String) -> Self {
        Self {
            resource_type,
            id,
            name,
            usage_count: 0,
            memory_size: 0,
        }
    }
    
    pub fn increment_usage(&mut self) {
        self.usage_count += 1;
    }
    
    pub fn set_memory_size(&mut self, size: usize) {
        self.memory_size = size;
    }
}
