/// Font management system
/// 
/// This module provides comprehensive font management including
/// font loading, embedding, metrics calculation, and optimization.

use super::*;
use crate::document::{FontWeight, FontStyle};
use crate::error::{PDFError, PDFResult};

use std::collections::HashMap;
use serde::{Deserialize, Serialize};

/// Font manager for handling font resources
#[derive(Debug)]
pub struct FontManager {
    /// Loaded fonts
    fonts: HashMap<String, FontResource>,
    /// Font metrics cache
    metrics_cache: HashMap<String, FontMetrics>,
    /// System fonts
    system_fonts: Vec<SystemFont>,
    /// Embedded fonts
    embedded_fonts: HashMap<String, Vec<u8>>,
    /// Font substitution rules
    substitution_rules: HashMap<String, String>,
}

impl FontManager {
    /// Create a new font manager
    pub fn new() -> Self {
        let mut manager = Self {
            fonts: HashMap::new(),
            metrics_cache: HashMap::new(),
            system_fonts: Vec::new(),
            embedded_fonts: HashMap::new(),
            substitution_rules: HashMap::new(),
        };
        
        // Initialize with standard fonts
        manager.initialize_standard_fonts();
        manager.load_system_fonts();
        
        manager
    }
    
    /// Load a font from data
    pub fn load_font(&mut self, name: String, data: Vec<u8>) -> PDFResult<String> {
        let font_id = format!("font_{}", self.fonts.len());
        
        // Parse font data to extract metadata
        let font_info = self.parse_font_data(&data)?;
        
        let font_resource = FontResource {
            id: font_id.clone(),
            name: name.clone(),
            family: font_info.family,
            weight: font_info.weight,
            style: font_info.style,
            data: Some(data.clone()),
            embedded: true,
            metrics: font_info.metrics,
        };
        
        self.fonts.insert(font_id.clone(), font_resource);
        self.embedded_fonts.insert(name, data);
        
        Ok(font_id)
    }
    
    /// Get a font by name
    pub fn get_font(&self, name: &str) -> Option<&FontResource> {
        // First try exact match
        if let Some(font) = self.fonts.values().find(|f| f.name == name) {
            return Some(font);
        }
        
        // Try family match
        if let Some(font) = self.fonts.values().find(|f| f.family == name) {
            return Some(font);
        }
        
        // Try substitution
        if let Some(substitute) = self.substitution_rules.get(name) {
            return self.get_font(substitute);
        }
        
        None
    }
    
    /// Get font by ID
    pub fn get_font_by_id(&self, id: &str) -> Option<&FontResource> {
        self.fonts.get(id)
    }
    
    /// Get font metrics
    pub fn get_font_metrics(&mut self, font_name: &str, size: f32) -> PDFResult<FontMetrics> {
        let cache_key = format!("{}_{}", font_name, size);
        
        if let Some(metrics) = self.metrics_cache.get(&cache_key) {
            return Ok(metrics.clone());
        }
        
        let font = self.get_font(font_name)
            .ok_or_else(|| PDFError::ConfigError(format!("Font '{}' not found", font_name)))?;
        
        let mut metrics = font.metrics.clone();
        metrics.scale_to_size(size);
        
        self.metrics_cache.insert(cache_key, metrics.clone());
        Ok(metrics)
    }
    
    /// Calculate text width
    pub fn calculate_text_width(&mut self, text: &str, font_name: &str, size: f32) -> PDFResult<f32> {
        let metrics = self.get_font_metrics(font_name, size)?;
        
        let mut width = 0.0;
        for ch in text.chars() {
            width += metrics.get_char_width(ch);
        }
        
        Ok(width)
    }
    
    /// Calculate text height
    pub fn calculate_text_height(&mut self, font_name: &str, size: f32) -> PDFResult<f32> {
        let metrics = self.get_font_metrics(font_name, size)?;
        Ok(metrics.line_height)
    }
    
    /// Add font substitution rule
    pub fn add_substitution(&mut self, from: String, to: String) {
        self.substitution_rules.insert(from, to);
    }
    
    /// List available fonts
    pub fn list_fonts(&self) -> Vec<&FontResource> {
        self.fonts.values().collect()
    }
    
    /// List system fonts
    pub fn list_system_fonts(&self) -> &[SystemFont] {
        &self.system_fonts
    }
    
    /// Check if font is embedded
    pub fn is_embedded(&self, font_name: &str) -> bool {
        self.embedded_fonts.contains_key(font_name)
    }
    
    /// Get embedded font data
    pub fn get_embedded_font_data(&self, font_name: &str) -> Option<&[u8]> {
        self.embedded_fonts.get(font_name).map(|v| v.as_slice())
    }
    
    /// Clear font cache
    pub fn clear_cache(&mut self) {
        self.metrics_cache.clear();
    }
    
    /// Get memory usage
    pub fn memory_usage(&self) -> usize {
        let mut size = 0;
        
        // Font data
        for data in self.embedded_fonts.values() {
            size += data.len();
        }
        
        // Metrics cache (rough estimate)
        size += self.metrics_cache.len() * std::mem::size_of::<FontMetrics>();
        
        size
    }
    
    /// Optimize font usage
    pub fn optimize(&mut self) -> PDFResult<super::OptimizationResult> {
        let mut result = super::OptimizationResult::default();
        
        // Remove unused fonts
        let mut unused_fonts = Vec::new();
        for (id, font) in &self.fonts {
            if font.usage_count == 0 {
                unused_fonts.push(id.clone());
            }
        }
        
        for id in unused_fonts {
            if let Some(font) = self.fonts.remove(&id) {
                if let Some(data) = self.embedded_fonts.remove(&font.name) {
                    result.memory_saved += data.len();
                    result.fonts_optimized += 1;
                }
            }
        }
        
        // Clear metrics cache for unused fonts
        self.metrics_cache.retain(|key, _| {
            let font_name = key.split('_').next().unwrap_or("");
            self.fonts.values().any(|f| f.name == font_name)
        });
        
        Ok(result)
    }
    
    /// Validate fonts
    pub fn validate(&self) -> super::ResourceValidationResult {
        let mut result = super::ResourceValidationResult {
            is_valid: true,
            errors: Vec::new(),
            warnings: Vec::new(),
        };
        
        // Check for missing fonts
        for font in self.fonts.values() {
            if font.embedded && font.data.is_none() {
                result.errors.push(format!("Embedded font '{}' has no data", font.name));
                result.is_valid = false;
            }
        }
        
        // Check for large embedded fonts
        for (name, data) in &self.embedded_fonts {
            if data.len() > 5 * 1024 * 1024 { // 5MB
                result.warnings.push(format!("Font '{}' is very large ({}MB)", name, data.len() / (1024 * 1024)));
            }
        }
        
        result
    }
    
    /// Initialize standard PDF fonts
    fn initialize_standard_fonts(&mut self) {
        let standard_fonts = [
            ("Helvetica", "Helvetica", FontWeight::Normal, FontStyle::Normal),
            ("Helvetica-Bold", "Helvetica", FontWeight::Bold, FontStyle::Normal),
            ("Helvetica-Oblique", "Helvetica", FontWeight::Normal, FontStyle::Italic),
            ("Helvetica-BoldOblique", "Helvetica", FontWeight::Bold, FontStyle::Italic),
            ("Times-Roman", "Times", FontWeight::Normal, FontStyle::Normal),
            ("Times-Bold", "Times", FontWeight::Bold, FontStyle::Normal),
            ("Times-Italic", "Times", FontWeight::Normal, FontStyle::Italic),
            ("Times-BoldItalic", "Times", FontWeight::Bold, FontStyle::Italic),
            ("Courier", "Courier", FontWeight::Normal, FontStyle::Normal),
            ("Courier-Bold", "Courier", FontWeight::Bold, FontStyle::Normal),
            ("Courier-Oblique", "Courier", FontWeight::Normal, FontStyle::Italic),
            ("Courier-BoldOblique", "Courier", FontWeight::Bold, FontStyle::Italic),
        ];
        
        for (name, family, weight, style) in &standard_fonts {
            let font_id = format!("std_{}", name.to_lowercase().replace('-', "_"));
            let font_resource = FontResource {
                id: font_id.clone(),
                name: name.to_string(),
                family: family.to_string(),
                weight: *weight,
                style: *style,
                data: None,
                embedded: false,
                metrics: FontMetrics::default_for_standard_font(name),
            };
            
            self.fonts.insert(font_id, font_resource);
        }
    }
    
    /// Load system fonts (placeholder)
    fn load_system_fonts(&mut self) {
        // In a real implementation, this would scan the system for available fonts
        // For now, we'll add some common system fonts
        let system_fonts = [
            ("Arial", "Arial"),
            ("Times New Roman", "Times New Roman"),
            ("Courier New", "Courier New"),
            ("Verdana", "Verdana"),
            ("Georgia", "Georgia"),
        ];
        
        for (name, family) in &system_fonts {
            self.system_fonts.push(SystemFont {
                name: name.to_string(),
                family: family.to_string(),
                path: None, // Would be populated in real implementation
                available: true,
            });
        }
    }
    
    /// Parse font data to extract metadata (placeholder)
    fn parse_font_data(&self, _data: &[u8]) -> PDFResult<FontInfo> {
        // In a real implementation, this would parse the font file
        // to extract family, weight, style, and metrics
        Ok(FontInfo {
            family: "Unknown".to_string(),
            weight: FontWeight::Normal,
            style: FontStyle::Normal,
            metrics: FontMetrics::default(),
        })
    }
}

/// Font resource information
#[derive(Debug, Clone)]
pub struct FontResource {
    pub id: String,
    pub name: String,
    pub family: String,
    pub weight: FontWeight,
    pub style: FontStyle,
    pub data: Option<Vec<u8>>,
    pub embedded: bool,
    pub metrics: FontMetrics,
    pub usage_count: u32,
}

/// Font metrics for layout calculations
#[derive(Debug, Clone)]
pub struct FontMetrics {
    pub ascent: f32,
    pub descent: f32,
    pub line_height: f32,
    pub cap_height: f32,
    pub x_height: f32,
    pub char_widths: HashMap<char, f32>,
    pub average_width: f32,
    pub max_width: f32,
}

impl FontMetrics {
    pub fn default() -> Self {
        Self {
            ascent: 0.8,
            descent: -0.2,
            line_height: 1.2,
            cap_height: 0.7,
            x_height: 0.5,
            char_widths: HashMap::new(),
            average_width: 0.5,
            max_width: 1.0,
        }
    }
    
    pub fn default_for_standard_font(font_name: &str) -> Self {
        let mut metrics = Self::default();
        
        // Adjust metrics based on font
        match font_name {
            name if name.starts_with("Courier") => {
                metrics.average_width = 0.6; // Monospace
                metrics.max_width = 0.6;
            },
            name if name.starts_with("Times") => {
                metrics.average_width = 0.45;
                metrics.max_width = 0.9;
            },
            _ => {
                metrics.average_width = 0.5;
                metrics.max_width = 0.8;
            }
        }
        
        metrics
    }
    
    pub fn scale_to_size(&mut self, size: f32) {
        self.ascent *= size;
        self.descent *= size;
        self.line_height *= size;
        self.cap_height *= size;
        self.x_height *= size;
        self.average_width *= size;
        self.max_width *= size;
        
        for width in self.char_widths.values_mut() {
            *width *= size;
        }
    }
    
    pub fn get_char_width(&self, ch: char) -> f32 {
        self.char_widths.get(&ch).copied().unwrap_or(self.average_width)
    }
}

/// System font information
#[derive(Debug, Clone)]
pub struct SystemFont {
    pub name: String,
    pub family: String,
    pub path: Option<String>,
    pub available: bool,
}

/// Font information extracted from font data
#[derive(Debug, Clone)]
struct FontInfo {
    pub family: String,
    pub weight: FontWeight,
    pub style: FontStyle,
    pub metrics: FontMetrics,
}
