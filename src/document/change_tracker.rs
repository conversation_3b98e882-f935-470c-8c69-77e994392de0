/// Change tracking system for undo/redo functionality
/// 
/// This module provides comprehensive change tracking for all document
/// modifications, enabling undo/redo operations and change history.

use super::*;
use crate::types::*;
use crate::error::{PDFError, PDFResult};

use std::collections::VecDeque;
use serde::{Deserialize, Serialize};

/// Change tracker for document modifications
#[derive(Debug)]
pub struct ChangeTracker {
    /// Maximum number of changes to track
    max_changes: usize,
    /// Undo stack
    undo_stack: VecDeque<DocumentChange>,
    /// Redo stack
    redo_stack: VecDeque<DocumentChange>,
    /// Current change group ID (for batching related changes)
    current_group_id: Option<String>,
    /// Whether change tracking is enabled
    enabled: bool,
}

impl ChangeTracker {
    /// Create a new change tracker
    pub fn new(max_changes: usize) -> Self {
        Self {
            max_changes,
            undo_stack: VecDeque::new(),
            redo_stack: VecDeque::new(),
            current_group_id: None,
            enabled: true,
        }
    }
    
    /// Enable or disable change tracking
    pub fn set_enabled(&mut self, enabled: bool) {
        self.enabled = enabled;
    }
    
    /// Check if change tracking is enabled
    pub fn is_enabled(&self) -> bool {
        self.enabled
    }
    
    /// Record a new change
    pub fn record_change(&mut self, change: DocumentChange) {
        if !self.enabled {
            return;
        }
        
        // Clear redo stack when a new change is made
        self.redo_stack.clear();
        
        // Add to undo stack
        self.undo_stack.push_back(change);
        
        // Maintain maximum size
        if self.undo_stack.len() > self.max_changes {
            self.undo_stack.pop_front();
        }
    }
    
    /// Start a change group (for batching related changes)
    pub fn start_group(&mut self, group_name: String) {
        self.current_group_id = Some(group_name);
    }
    
    /// End the current change group
    pub fn end_group(&mut self) {
        self.current_group_id = None;
    }
    
    /// Check if there are changes to undo
    pub fn can_undo(&self) -> bool {
        !self.undo_stack.is_empty()
    }
    
    /// Check if there are changes to redo
    pub fn can_redo(&self) -> bool {
        !self.redo_stack.is_empty()
    }
    
    /// Get the next change to undo
    pub fn peek_undo(&self) -> Option<&DocumentChange> {
        self.undo_stack.back()
    }
    
    /// Get the next change to redo
    pub fn peek_redo(&self) -> Option<&DocumentChange> {
        self.redo_stack.back()
    }
    
    /// Undo the last change
    pub fn undo(&mut self) -> Option<DocumentChange> {
        if let Some(change) = self.undo_stack.pop_back() {
            self.redo_stack.push_back(change.clone());
            
            // Maintain maximum size for redo stack
            if self.redo_stack.len() > self.max_changes {
                self.redo_stack.pop_front();
            }
            
            Some(change)
        } else {
            None
        }
    }
    
    /// Redo the last undone change
    pub fn redo(&mut self) -> Option<DocumentChange> {
        if let Some(change) = self.redo_stack.pop_back() {
            self.undo_stack.push_back(change.clone());
            Some(change)
        } else {
            None
        }
    }
    
    /// Clear all change history
    pub fn clear(&mut self) {
        self.undo_stack.clear();
        self.redo_stack.clear();
        self.current_group_id = None;
    }
    
    /// Get the number of changes in undo stack
    pub fn undo_count(&self) -> usize {
        self.undo_stack.len()
    }
    
    /// Get the number of changes in redo stack
    pub fn redo_count(&self) -> usize {
        self.redo_stack.len()
    }
    
    /// Get change history summary
    pub fn get_history(&self) -> ChangeHistory {
        ChangeHistory {
            undo_changes: self.undo_stack.iter().cloned().collect(),
            redo_changes: self.redo_stack.iter().cloned().collect(),
            can_undo: self.can_undo(),
            can_redo: self.can_redo(),
        }
    }
}

/// Document change representation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DocumentChange {
    /// Unique change ID
    pub id: String,
    /// Change type
    pub change_type: ChangeType,
    /// Page number (if applicable)
    pub page: Option<u32>,
    /// Change description
    pub description: String,
    /// Timestamp when change was made
    pub timestamp: u64,
    /// Author of the change
    pub author: Option<String>,
    /// Group ID for batched changes
    pub group_id: Option<String>,
    /// Previous state (for undo)
    pub previous_state: ChangeState,
    /// New state (for redo)
    pub new_state: ChangeState,
}

impl DocumentChange {
    /// Create a new document change
    pub fn new(
        change_type: ChangeType,
        page: Option<u32>,
        description: String,
        previous_state: ChangeState,
        new_state: ChangeState,
    ) -> Self {
        Self {
            id: Self::generate_id(),
            change_type,
            page,
            description,
            timestamp: js_sys::Date::now() as u64,
            author: None,
            group_id: None,
            previous_state,
            new_state,
        }
    }
    
    /// Generate a unique change ID
    fn generate_id() -> String {
        format!("change_{}", js_sys::Date::now() as u64)
    }
    
    /// Set the author of the change
    pub fn with_author(mut self, author: String) -> Self {
        self.author = Some(author);
        self
    }
    
    /// Set the group ID for the change
    pub fn with_group_id(mut self, group_id: String) -> Self {
        self.group_id = Some(group_id);
        self
    }
}

/// Change type enumeration
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum ChangeType {
    // Text changes
    TextInsert,
    TextDelete,
    TextFormat,
    TextMove,
    
    // Page changes
    PageInsert,
    PageDelete,
    PageMove,
    PageResize,
    PageRotate,
    
    // Object changes
    ObjectInsert,
    ObjectDelete,
    ObjectMove,
    ObjectResize,
    ObjectFormat,
    
    // Image changes
    ImageInsert,
    ImageDelete,
    ImageResize,
    ImageMove,
    
    // Annotation changes
    AnnotationAdd,
    AnnotationEdit,
    AnnotationDelete,
    
    // Form changes
    FormFieldAdd,
    FormFieldEdit,
    FormFieldDelete,
    
    // Document structure changes
    MetadataChange,
    PermissionChange,
    StructureChange,
    
    // Batch changes
    BatchChange,
}

/// Change state representation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ChangeState {
    /// Text content state
    Text {
        content: String,
        position: usize,
        format: Option<TextFormat>,
    },
    
    /// Page state
    Page {
        page_number: u32,
        settings: PageSettings,
        exists: bool,
    },
    
    /// Object state
    Object {
        object_id: String,
        object_type: String,
        bounds: Rect,
        properties: std::collections::HashMap<String, String>,
        exists: bool,
    },
    
    /// Image state
    ImageState {
        page: u32,
        image_id: String,
        bounds: Rect,
        exists: bool,
    },

    /// Shape state
    ShapeState {
        page: u32,
        shape_id: String,
        shape_type: String,
        bounds: Rect,
        exists: bool,
    },
    
    /// Annotation state
    Annotation {
        annotation_id: String,
        annotation_type: AnnotationType,
        bounds: Rect,
        content: String,
        exists: bool,
    },
    
    /// Form field state
    FormField {
        field_name: String,
        field_type: FormFieldType,
        bounds: Rect,
        value: String,
        exists: bool,
    },
    
    /// Document metadata state
    Metadata {
        title: Option<String>,
        author: Option<String>,
        subject: Option<String>,
        keywords: Option<String>,
    },
    
    /// Batch of multiple states
    Batch {
        states: Vec<ChangeState>,
    },
    
    /// Empty state (for new objects)
    Empty,
}

/// Change history summary
#[derive(Debug, Clone)]
pub struct ChangeHistory {
    pub undo_changes: Vec<DocumentChange>,
    pub redo_changes: Vec<DocumentChange>,
    pub can_undo: bool,
    pub can_redo: bool,
}

/// Change event for notifications
#[derive(Debug, Clone)]
pub struct ChangeEvent {
    pub change: DocumentChange,
    pub event_type: ChangeEventType,
}

/// Change event type
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ChangeEventType {
    Applied,
    Undone,
    Redone,
}

/// Change listener trait for observing changes
pub trait ChangeListener {
    fn on_change(&mut self, event: ChangeEvent);
}

/// Change notification system
#[derive(Debug)]
pub struct ChangeNotifier {
    listeners: Vec<Box<dyn ChangeListener>>,
}

impl ChangeNotifier {
    pub fn new() -> Self {
        Self {
            listeners: Vec::new(),
        }
    }
    
    pub fn add_listener(&mut self, listener: Box<dyn ChangeListener>) {
        self.listeners.push(listener);
    }
    
    pub fn notify(&mut self, event: ChangeEvent) {
        for listener in &mut self.listeners {
            listener.on_change(event.clone());
        }
    }
}

/// Utility functions for creating common change states
impl ChangeState {
    /// Create a text change state
    pub fn text(content: String, position: usize, format: Option<TextFormat>) -> Self {
        ChangeState::Text { content, position, format }
    }
    
    /// Create a page change state
    pub fn page(page_number: u32, settings: PageSettings, exists: bool) -> Self {
        ChangeState::Page { page_number, settings, exists }
    }
    
    /// Create an empty state
    pub fn empty() -> Self {
        ChangeState::Empty
    }
    
    /// Create a batch state
    pub fn batch(states: Vec<ChangeState>) -> Self {
        ChangeState::Batch { states }
    }
}
