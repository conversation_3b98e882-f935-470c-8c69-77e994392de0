/// Editable PDF page structure
/// 
/// This module provides the editable page structure that can contain
/// text objects, graphics, annotations, and other page elements.

use super::*;
use crate::types::*;
use crate::error::{PDFError, PDFResult};

use std::collections::HashMap;
use serde::{Deserialize, Serialize};

/// Editable page structure
#[derive(Debug, Clone)]
pub struct EditablePage {
    /// Page number (1-based)
    page_number: u32,
    /// Page settings (size, margins, etc.)
    settings: PageSettings,
    /// Page content layers
    layers: Vec<PageLayer>,
    /// Page annotations
    annotations: Vec<Annotation>,
    /// Form fields on this page
    form_fields: Vec<FormField>,
    /// Page-level metadata
    metadata: PageMetadata,
    /// Whether the page has been modified
    modified: bool,
}

impl EditablePage {
    /// Create a new editable page
    pub fn new(page_number: u32, settings: PageSettings) -> PDFResult<Self> {
        let mut layers = Vec::new();
        
        // Add default background layer
        layers.push(PageLayer::new(
            "background".to_string(),
            LayerType::Background,
            true,
        ));
        
        // Add default content layer
        layers.push(PageLayer::new(
            "content".to_string(),
            LayerType::Content,
            true,
        ));
        
        // Add default annotation layer
        layers.push(PageLayer::new(
            "annotations".to_string(),
            LayerType::Annotations,
            true,
        ));
        
        Ok(EditablePage {
            page_number,
            settings,
            layers,
            annotations: Vec::new(),
            form_fields: Vec::new(),
            metadata: PageMetadata::new(),
            modified: false,
        })
    }
    
    /// Get page number
    pub fn page_number(&self) -> u32 {
        self.page_number
    }
    
    /// Set page number
    pub fn set_page_number(&mut self, page_number: u32) {
        self.page_number = page_number;
        self.mark_modified();
    }
    
    /// Get page settings
    pub fn settings(&self) -> &PageSettings {
        &self.settings
    }
    
    /// Get mutable page settings
    pub fn settings_mut(&mut self) -> &mut PageSettings {
        self.mark_modified();
        &mut self.settings
    }
    
    /// Get page width
    pub fn width(&self) -> f32 {
        self.settings.width
    }
    
    /// Get page height
    pub fn height(&self) -> f32 {
        self.settings.height
    }
    
    /// Get page bounds
    pub fn bounds(&self) -> Rect {
        Rect::new(0.0, 0.0, self.settings.width, self.settings.height)
    }
    
    /// Get content area (excluding margins)
    pub fn content_area(&self) -> Rect {
        Rect::new(
            self.settings.margin_left,
            self.settings.margin_top,
            self.settings.width - self.settings.margin_left - self.settings.margin_right,
            self.settings.height - self.settings.margin_top - self.settings.margin_bottom,
        )
    }
    
    /// Get all layers
    pub fn layers(&self) -> &[PageLayer] {
        &self.layers
    }
    
    /// Get a layer by name
    pub fn get_layer(&self, name: &str) -> Option<&PageLayer> {
        self.layers.iter().find(|layer| layer.name == name)
    }
    
    /// Get a mutable layer by name
    pub fn get_layer_mut(&mut self, name: &str) -> Option<&mut PageLayer> {
        self.mark_modified();
        self.layers.iter_mut().find(|layer| layer.name == name)
    }
    
    /// Add a new layer
    pub fn add_layer(&mut self, layer: PageLayer) -> PDFResult<()> {
        // Check if layer name already exists
        if self.layers.iter().any(|l| l.name == layer.name) {
            return Err(PDFError::ConfigError(
                format!("Layer '{}' already exists", layer.name)
            ));
        }
        
        self.layers.push(layer);
        self.mark_modified();
        Ok(())
    }
    
    /// Remove a layer by name
    pub fn remove_layer(&mut self, name: &str) -> PDFResult<()> {
        let index = self.layers.iter().position(|l| l.name == name)
            .ok_or_else(|| PDFError::ConfigError(
                format!("Layer '{}' not found", name)
            ))?;
        
        // Don't allow removing essential layers
        let layer_type = self.layers[index].layer_type;
        if matches!(layer_type, LayerType::Background | LayerType::Content) {
            return Err(PDFError::ConfigError(
                "Cannot remove essential layers".to_string()
            ));
        }
        
        self.layers.remove(index);
        self.mark_modified();
        Ok(())
    }
    
    /// Get all annotations
    pub fn annotations(&self) -> &[Annotation] {
        &self.annotations
    }
    
    /// Add an annotation
    pub fn add_annotation(&mut self, annotation: Annotation) -> PDFResult<()> {
        self.annotations.push(annotation);
        self.mark_modified();
        Ok(())
    }
    
    /// Remove an annotation by ID
    pub fn remove_annotation(&mut self, annotation_id: &str) -> PDFResult<()> {
        let index = self.annotations.iter().position(|a| a.id == annotation_id)
            .ok_or_else(|| PDFError::ConfigError(
                format!("Annotation '{}' not found", annotation_id)
            ))?;
        
        self.annotations.remove(index);
        self.mark_modified();
        Ok(())
    }
    
    /// Get all form fields
    pub fn form_fields(&self) -> &[FormField] {
        &self.form_fields
    }
    
    /// Add a form field
    pub fn add_form_field(&mut self, field: FormField) -> PDFResult<()> {
        self.form_fields.push(field);
        self.mark_modified();
        Ok(())
    }
    
    /// Remove a form field by name
    pub fn remove_form_field(&mut self, field_name: &str) -> PDFResult<()> {
        let index = self.form_fields.iter().position(|f| f.name == field_name)
            .ok_or_else(|| PDFError::ConfigError(
                format!("Form field '{}' not found", field_name)
            ))?;
        
        self.form_fields.remove(index);
        self.mark_modified();
        Ok(())
    }
    
    /// Check if the page has been modified
    pub fn is_modified(&self) -> bool {
        self.modified
    }
    
    /// Mark the page as modified
    fn mark_modified(&mut self) {
        self.modified = true;
    }
    
    /// Reset the modified flag
    pub fn reset_modified(&mut self) {
        self.modified = false;
    }
    
    /// Validate the page structure
    pub fn validate(&self) -> ValidationResult {
        let mut errors = Vec::new();
        let mut warnings = Vec::new();
        
        // Validate page dimensions
        if self.settings.width <= 0.0 || self.settings.height <= 0.0 {
            errors.push(ValidationError {
                page: Some(self.page_number),
                message: "Page dimensions must be positive".to_string(),
                error_type: ValidationErrorType::InvalidStructure,
            });
        }
        
        // Validate margins
        if self.settings.margin_left + self.settings.margin_right >= self.settings.width {
            errors.push(ValidationError {
                page: Some(self.page_number),
                message: "Horizontal margins exceed page width".to_string(),
                error_type: ValidationErrorType::InvalidStructure,
            });
        }
        
        if self.settings.margin_top + self.settings.margin_bottom >= self.settings.height {
            errors.push(ValidationError {
                page: Some(self.page_number),
                message: "Vertical margins exceed page height".to_string(),
                error_type: ValidationErrorType::InvalidStructure,
            });
        }
        
        // Validate layers
        for layer in &self.layers {
            let layer_validation = layer.validate();
            errors.extend(layer_validation.errors);
            warnings.extend(layer_validation.warnings);
        }
        
        ValidationResult {
            is_valid: errors.is_empty(),
            errors,
            warnings,
        }
    }
}

/// Page layer for organizing content
#[derive(Debug, Clone)]
pub struct PageLayer {
    /// Layer name
    pub name: String,
    /// Layer type
    pub layer_type: LayerType,
    /// Whether the layer is visible
    pub visible: bool,
    /// Whether the layer is locked
    pub locked: bool,
    /// Layer opacity (0.0 to 1.0)
    pub opacity: f32,
    /// Layer content objects
    pub objects: Vec<PageObject>,
}

impl PageLayer {
    pub fn new(name: String, layer_type: LayerType, visible: bool) -> Self {
        Self {
            name,
            layer_type,
            visible,
            locked: false,
            opacity: 1.0,
            objects: Vec::new(),
        }
    }
    
    /// Add an object to the layer
    pub fn add_object(&mut self, object: PageObject) {
        self.objects.push(object);
    }
    
    /// Remove an object by ID
    pub fn remove_object(&mut self, object_id: &str) -> PDFResult<()> {
        let index = self.objects.iter().position(|o| o.id() == object_id)
            .ok_or_else(|| PDFError::ConfigError(
                format!("Object '{}' not found", object_id)
            ))?;
        
        self.objects.remove(index);
        Ok(())
    }
    
    /// Validate the layer
    pub fn validate(&self) -> ValidationResult {
        let mut errors = Vec::new();
        let mut warnings = Vec::new();
        
        // Validate opacity
        if self.opacity < 0.0 || self.opacity > 1.0 {
            errors.push(ValidationError {
                page: None,
                message: "Layer opacity must be between 0.0 and 1.0".to_string(),
                error_type: ValidationErrorType::InvalidStructure,
            });
        }
        
        ValidationResult {
            is_valid: errors.is_empty(),
            errors,
            warnings,
        }
    }
}

/// Layer type enumeration
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum LayerType {
    Background,
    Content,
    Graphics,
    Text,
    Images,
    Annotations,
    Forms,
    Custom,
}

/// Page object enumeration
#[derive(Debug, Clone)]
pub enum PageObject {
    Text(TextObject),
    Image(ImageObject),
    Shape(ShapeObject),
    Line(LineObject),
}

impl PageObject {
    pub fn id(&self) -> &str {
        match self {
            PageObject::Text(obj) => &obj.id,
            PageObject::Image(obj) => &obj.id,
            PageObject::Shape(obj) => &obj.id,
            PageObject::Line(obj) => &obj.id,
        }
    }
    
    pub fn bounds(&self) -> Rect {
        match self {
            PageObject::Text(obj) => obj.bounds,
            PageObject::Image(obj) => obj.bounds,
            PageObject::Shape(obj) => obj.bounds,
            PageObject::Line(obj) => Rect::new(
                obj.start.x.min(obj.end.x),
                obj.start.y.min(obj.end.y),
                (obj.end.x - obj.start.x).abs(),
                (obj.end.y - obj.start.y).abs(),
            ),
        }
    }
}

/// Text object
#[derive(Debug, Clone)]
pub struct TextObject {
    pub id: String,
    pub text: String,
    pub bounds: Rect,
    pub format: TextFormat,
    pub alignment: TextAlignment,
}

/// Text alignment enumeration
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum TextAlignment {
    Left,
    Center,
    Right,
    Justify,
}

/// Image object
#[derive(Debug, Clone)]
pub struct ImageObject {
    pub id: String,
    pub bounds: Rect,
    pub image_data: Vec<u8>,
    pub format: ImageFormat,
    pub opacity: f32,
}

/// Shape object
#[derive(Debug, Clone)]
pub struct ShapeObject {
    pub id: String,
    pub bounds: Rect,
    pub shape_type: ShapeType,
    pub fill_color: Option<Color>,
    pub stroke_color: Option<Color>,
    pub stroke_width: f32,
}

/// Shape type enumeration
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ShapeType {
    Rectangle,
    Circle,
    Ellipse,
    Polygon,
}

/// Line object
#[derive(Debug, Clone)]
pub struct LineObject {
    pub id: String,
    pub start: Point,
    pub end: Point,
    pub color: Color,
    pub width: f32,
    pub style: LineStyle,
}

/// Line style enumeration
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum LineStyle {
    Solid,
    Dashed,
    Dotted,
}

/// Annotation structure
#[derive(Debug, Clone)]
pub struct Annotation {
    pub id: String,
    pub annotation_type: AnnotationType,
    pub bounds: Rect,
    pub content: String,
    pub author: Option<String>,
    pub created: u64,
    pub modified: u64,
}

/// Annotation type enumeration
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum AnnotationType {
    Text,
    Highlight,
    Note,
    Link,
    Stamp,
}

/// Form field structure
#[derive(Debug, Clone)]
pub struct FormField {
    pub name: String,
    pub field_type: FormFieldType,
    pub bounds: Rect,
    pub value: String,
    pub required: bool,
    pub readonly: bool,
}

/// Form field type enumeration
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum FormFieldType {
    Text,
    Checkbox,
    RadioButton,
    Dropdown,
    Signature,
}

/// Page metadata
#[derive(Debug, Clone)]
pub struct PageMetadata {
    pub created: u64,
    pub modified: u64,
    pub author: Option<String>,
    pub notes: Vec<String>,
}

impl PageMetadata {
    pub fn new() -> Self {
        let now = js_sys::Date::now() as u64;
        Self {
            created: now,
            modified: now,
            author: None,
            notes: Vec::new(),
        }
    }
}
