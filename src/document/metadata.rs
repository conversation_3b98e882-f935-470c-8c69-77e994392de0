/// Document metadata management
/// 
/// This module provides comprehensive document metadata handling,
/// including standard PDF metadata and custom properties.

use super::*;
use crate::types::*;
use crate::error::{PDFError, PDFResult};

use std::collections::HashMap;
use serde::{Deserialize, Serialize};

/// Extended document metadata for editing
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DocumentMetadata {
    /// Standard PDF metadata
    pub standard: StandardMetadata,
    /// Custom properties
    pub custom: HashMap<String, String>,
    /// Editing metadata
    pub editing: EditingMetadata,
    /// Version information
    pub version: VersionInfo,
}

impl DocumentMetadata {
    /// Create new document metadata
    pub fn new() -> Self {
        Self {
            standard: StandardMetadata::new(),
            custom: HashMap::new(),
            editing: EditingMetadata::new(),
            version: VersionInfo::new(),
        }
    }
    
    /// Create metadata from existing document info
    pub fn from_document_info(info: &DocumentInfo) -> Self {
        let mut metadata = Self::new();
        
        metadata.standard.title = info.title();
        metadata.standard.author = info.author();
        metadata.standard.subject = info.subject();
        metadata.standard.creator = info.creator();
        metadata.standard.producer = info.producer();
        metadata.standard.creation_date = info.creation_date();
        metadata.standard.modification_date = info.modification_date();
        
        metadata
    }
    
    /// Update standard metadata
    pub fn update_standard(&mut self, field: StandardField, value: Option<String>) {
        match field {
            StandardField::Title => self.standard.title = value,
            StandardField::Author => self.standard.author = value,
            StandardField::Subject => self.standard.subject = value,
            StandardField::Keywords => self.standard.keywords = value,
            StandardField::Creator => self.standard.creator = value,
            StandardField::Producer => self.standard.producer = value,
        }
        
        self.editing.last_modified = Self::current_timestamp();
        self.editing.modification_count += 1;
    }
    
    /// Set a custom property
    pub fn set_custom_property(&mut self, key: String, value: String) {
        self.custom.insert(key, value);
        self.editing.last_modified = Self::current_timestamp();
    }
    
    /// Get a custom property
    pub fn get_custom_property(&self, key: &str) -> Option<&String> {
        self.custom.get(key)
    }
    
    /// Remove a custom property
    pub fn remove_custom_property(&mut self, key: &str) -> Option<String> {
        let result = self.custom.remove(key);
        if result.is_some() {
            self.editing.last_modified = Self::current_timestamp();
        }
        result
    }
    
    /// Get all custom property keys
    pub fn custom_property_keys(&self) -> Vec<&String> {
        self.custom.keys().collect()
    }
    
    /// Update editing metadata
    pub fn update_editing_info(&mut self, editor: Option<String>, comment: Option<String>) {
        if let Some(editor) = editor {
            self.editing.last_editor = Some(editor);
        }
        
        if let Some(comment) = comment {
            self.editing.edit_comments.push(EditComment {
                timestamp: Self::current_timestamp(),
                author: self.editing.last_editor.clone(),
                comment,
            });
        }
        
        self.editing.last_modified = Self::current_timestamp();
        self.editing.modification_count += 1;
    }
    
    /// Add a version entry
    pub fn add_version(&mut self, description: String, author: Option<String>) {
        let version = VersionEntry {
            version_number: self.version.versions.len() as u32 + 1,
            timestamp: Self::current_timestamp(),
            author,
            description,
            changes: Vec::new(),
        };
        
        self.version.versions.push(version);
        self.version.current_version = self.version.versions.len() as u32;
    }
    
    /// Get current timestamp
    fn current_timestamp() -> u64 {
        js_sys::Date::now() as u64
    }
    
    /// Validate metadata
    pub fn validate(&self) -> ValidationResult {
        let mut errors = Vec::new();
        let mut warnings = Vec::new();
        
        // Validate standard metadata
        if let Some(ref title) = self.standard.title {
            if title.len() > 1000 {
                warnings.push(ValidationWarning {
                    page: None,
                    message: "Title is very long and may be truncated".to_string(),
                    warning_type: ValidationWarningType::CompatibilityIssue,
                });
            }
        }
        
        // Validate custom properties
        for (key, value) in &self.custom {
            if key.len() > 255 {
                errors.push(ValidationError {
                    page: None,
                    message: format!("Custom property key '{}' is too long", key),
                    error_type: ValidationErrorType::InvalidStructure,
                });
            }
            
            if value.len() > 10000 {
                warnings.push(ValidationWarning {
                    page: None,
                    message: format!("Custom property '{}' has a very long value", key),
                    warning_type: ValidationWarningType::PerformanceImpact,
                });
            }
        }
        
        ValidationResult {
            is_valid: errors.is_empty(),
            errors,
            warnings,
        }
    }
}

/// Standard PDF metadata fields
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StandardMetadata {
    pub title: Option<String>,
    pub author: Option<String>,
    pub subject: Option<String>,
    pub keywords: Option<String>,
    pub creator: Option<String>,
    pub producer: Option<String>,
    pub creation_date: Option<String>,
    pub modification_date: Option<String>,
}

impl StandardMetadata {
    pub fn new() -> Self {
        Self {
            title: None,
            author: None,
            subject: None,
            keywords: None,
            creator: Some("PDF Render Engine".to_string()),
            producer: Some("PDF Render Engine".to_string()),
            creation_date: Some(Self::format_current_date()),
            modification_date: Some(Self::format_current_date()),
        }
    }
    
    fn format_current_date() -> String {
        let date = js_sys::Date::new_0();
        date.to_iso_string().as_string().unwrap_or_default()
    }
}

/// Standard metadata field enumeration
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum StandardField {
    Title,
    Author,
    Subject,
    Keywords,
    Creator,
    Producer,
}

/// Editing-specific metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EditingMetadata {
    /// When the document was first opened for editing
    pub editing_started: u64,
    /// Last modification timestamp
    pub last_modified: u64,
    /// Last editor name
    pub last_editor: Option<String>,
    /// Number of modifications made
    pub modification_count: u32,
    /// Total editing time in seconds
    pub total_edit_time: u32,
    /// Comments about edits
    pub edit_comments: Vec<EditComment>,
    /// Editing session information
    pub sessions: Vec<EditingSession>,
}

impl EditingMetadata {
    pub fn new() -> Self {
        let now = js_sys::Date::now() as u64;
        Self {
            editing_started: now,
            last_modified: now,
            last_editor: None,
            modification_count: 0,
            total_edit_time: 0,
            edit_comments: Vec::new(),
            sessions: Vec::new(),
        }
    }
    
    /// Start a new editing session
    pub fn start_session(&mut self, editor: Option<String>) {
        let session = EditingSession {
            start_time: js_sys::Date::now() as u64,
            end_time: None,
            editor,
            changes_made: 0,
        };
        
        self.sessions.push(session);
    }
    
    /// End the current editing session
    pub fn end_session(&mut self) {
        if let Some(session) = self.sessions.last_mut() {
            if session.end_time.is_none() {
                session.end_time = Some(js_sys::Date::now() as u64);
                
                if let Some(start) = session.start_time.checked_sub(0) {
                    let duration = (js_sys::Date::now() as u64 - start) / 1000; // Convert to seconds
                    self.total_edit_time += duration as u32;
                }
            }
        }
    }
    
    /// Record a change in the current session
    pub fn record_change(&mut self) {
        self.modification_count += 1;
        
        if let Some(session) = self.sessions.last_mut() {
            session.changes_made += 1;
        }
    }
}

/// Edit comment
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EditComment {
    pub timestamp: u64,
    pub author: Option<String>,
    pub comment: String,
}

/// Editing session information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EditingSession {
    pub start_time: u64,
    pub end_time: Option<u64>,
    pub editor: Option<String>,
    pub changes_made: u32,
}

/// Version information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VersionInfo {
    /// Current version number
    pub current_version: u32,
    /// Version history
    pub versions: Vec<VersionEntry>,
    /// Whether versioning is enabled
    pub versioning_enabled: bool,
}

impl VersionInfo {
    pub fn new() -> Self {
        Self {
            current_version: 1,
            versions: Vec::new(),
            versioning_enabled: false,
        }
    }
    
    /// Get the current version entry
    pub fn current_version_entry(&self) -> Option<&VersionEntry> {
        self.versions.get((self.current_version - 1) as usize)
    }
    
    /// Get version by number
    pub fn get_version(&self, version_number: u32) -> Option<&VersionEntry> {
        self.versions.iter().find(|v| v.version_number == version_number)
    }
}

/// Version entry
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VersionEntry {
    pub version_number: u32,
    pub timestamp: u64,
    pub author: Option<String>,
    pub description: String,
    pub changes: Vec<String>,
}

/// Metadata export format
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum MetadataFormat {
    JSON,
    XML,
    PDF,
    XMP,
}

impl DocumentMetadata {
    /// Export metadata in specified format
    pub fn export(&self, format: MetadataFormat) -> PDFResult<String> {
        match format {
            MetadataFormat::JSON => {
                serde_json::to_string_pretty(self)
                    .map_err(|e| PDFError::SerializationError(e.to_string()))
            },
            MetadataFormat::XML => {
                // XML export would be implemented here
                Err(PDFError::ConfigError("XML export not yet implemented".to_string()))
            },
            MetadataFormat::PDF => {
                // PDF metadata export would be implemented here
                Err(PDFError::ConfigError("PDF metadata export not yet implemented".to_string()))
            },
            MetadataFormat::XMP => {
                // XMP export would be implemented here
                Err(PDFError::ConfigError("XMP export not yet implemented".to_string()))
            },
        }
    }
    
    /// Import metadata from JSON
    pub fn import_json(json: &str) -> PDFResult<Self> {
        serde_json::from_str(json)
            .map_err(|e| PDFError::SerializationError(e.to_string()))
    }
}
