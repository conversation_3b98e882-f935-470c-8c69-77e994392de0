/// Text selection management
/// 
/// This module provides text selection functionality including
/// range selection, multi-selection, and selection operations.

use super::*;
use crate::types::{Point, Rect};

/// Text selection range
#[derive(Debug, <PERSON>lone, PartialEq, Eq)]
pub struct SelectionRange {
    /// Page number
    page: u32,
    /// Start position (inclusive)
    start: usize,
    /// End position (exclusive)
    end: usize,
}

impl SelectionRange {
    /// Create a new selection range
    pub fn new(page: u32, start: usize, end: usize) -> Self {
        let (start, end) = if start <= end {
            (start, end)
        } else {
            (end, start)
        };
        
        Self { page, start, end }
    }
    
    /// Get page number
    pub fn page(&self) -> u32 {
        self.page
    }
    
    /// Get start position
    pub fn start(&self) -> usize {
        self.start
    }
    
    /// Get end position
    pub fn end(&self) -> usize {
        self.end
    }
    
    /// Get selection length
    pub fn length(&self) -> usize {
        self.end - self.start
    }
    
    /// Check if range is empty
    pub fn is_empty(&self) -> bool {
        self.start == self.end
    }
    
    /// Check if position is within range
    pub fn contains(&self, position: usize) -> bool {
        position >= self.start && position < self.end
    }
    
    /// Extend range to include position
    pub fn extend_to(&mut self, position: usize) {
        if position < self.start {
            self.start = position;
        } else if position > self.end {
            self.end = position;
        }
    }
    
    /// Merge with another range (if they overlap or are adjacent)
    pub fn merge(&self, other: &SelectionRange) -> Option<SelectionRange> {
        if self.page != other.page {
            return None;
        }
        
        // Check if ranges overlap or are adjacent
        if self.end >= other.start && other.end >= self.start {
            Some(SelectionRange::new(
                self.page,
                self.start.min(other.start),
                self.end.max(other.end),
            ))
        } else {
            None
        }
    }
    
    /// Split range at position
    pub fn split_at(&self, position: usize) -> (Option<SelectionRange>, Option<SelectionRange>) {
        if position <= self.start || position >= self.end {
            return (Some(self.clone()), None);
        }
        
        let left = if position > self.start {
            Some(SelectionRange::new(self.page, self.start, position))
        } else {
            None
        };
        
        let right = if position < self.end {
            Some(SelectionRange::new(self.page, position, self.end))
        } else {
            None
        };
        
        (left, right)
    }
}

/// Text selection with support for multiple ranges
#[derive(Debug, Clone)]
pub struct TextSelection {
    /// Primary selection range
    primary_range: SelectionRange,
    /// Additional selection ranges (for multi-selection)
    additional_ranges: Vec<SelectionRange>,
    /// Selection direction (true = forward, false = backward)
    forward: bool,
    /// Visual bounds of selection
    visual_bounds: Vec<Rect>,
}

impl TextSelection {
    /// Create a new text selection
    pub fn new(range: SelectionRange) -> Self {
        Self {
            primary_range: range,
            additional_ranges: Vec::new(),
            forward: true,
            visual_bounds: Vec::new(),
        }
    }
    
    /// Create selection from start and end positions
    pub fn from_positions(page: u32, start: usize, end: usize) -> Self {
        let forward = end >= start;
        let range = SelectionRange::new(page, start, end);
        
        Self {
            primary_range: range,
            additional_ranges: Vec::new(),
            forward,
            visual_bounds: Vec::new(),
        }
    }
    
    /// Get primary selection range
    pub fn range(&self) -> &SelectionRange {
        &self.primary_range
    }
    
    /// Get all selection ranges
    pub fn ranges(&self) -> impl Iterator<Item = &SelectionRange> {
        std::iter::once(&self.primary_range).chain(self.additional_ranges.iter())
    }
    
    /// Get mutable primary range
    pub fn range_mut(&mut self) -> &mut SelectionRange {
        &mut self.primary_range
    }
    
    /// Check if selection is forward
    pub fn is_forward(&self) -> bool {
        self.forward
    }
    
    /// Set selection direction
    pub fn set_forward(&mut self, forward: bool) {
        self.forward = forward;
    }
    
    /// Check if selection is empty
    pub fn is_empty(&self) -> bool {
        self.primary_range.is_empty() && self.additional_ranges.is_empty()
    }
    
    /// Get total selected character count
    pub fn character_count(&self) -> usize {
        self.ranges().map(|r| r.length()).sum()
    }
    
    /// Get visual bounds
    pub fn visual_bounds(&self) -> &[Rect] {
        &self.visual_bounds
    }
    
    /// Set visual bounds
    pub fn set_visual_bounds(&mut self, bounds: Vec<Rect>) {
        self.visual_bounds = bounds;
    }
    
    /// Add additional selection range
    pub fn add_range(&mut self, range: SelectionRange) {
        // Try to merge with existing ranges first
        let mut merged = false;
        
        for existing_range in &mut self.additional_ranges {
            if let Some(merged_range) = existing_range.merge(&range) {
                *existing_range = merged_range;
                merged = true;
                break;
            }
        }
        
        // Try to merge with primary range
        if !merged {
            if let Some(merged_range) = self.primary_range.merge(&range) {
                self.primary_range = merged_range;
                merged = true;
            }
        }
        
        // Add as new range if no merge was possible
        if !merged {
            self.additional_ranges.push(range);
        }
        
        // Sort ranges by position
        self.sort_ranges();
    }
    
    /// Remove range at index
    pub fn remove_range(&mut self, index: usize) -> Option<SelectionRange> {
        if index == 0 {
            // Can't remove primary range, but can make it empty
            let old_range = self.primary_range.clone();
            self.primary_range = SelectionRange::new(old_range.page, old_range.start, old_range.start);
            Some(old_range)
        } else if index <= self.additional_ranges.len() {
            Some(self.additional_ranges.remove(index - 1))
        } else {
            None
        }
    }
    
    /// Clear all selection ranges
    pub fn clear(&mut self) {
        let page = self.primary_range.page;
        let start = self.primary_range.start;
        self.primary_range = SelectionRange::new(page, start, start);
        self.additional_ranges.clear();
        self.visual_bounds.clear();
    }
    
    /// Extend selection to position
    pub fn extend_to(&mut self, position: usize) {
        self.primary_range.extend_to(position);
        self.forward = position >= self.primary_range.start;
    }
    
    /// Select word at position
    pub fn select_word_at(&mut self, text: &str, position: usize) {
        let (start, end) = utils::find_word_boundaries(text, position);
        self.primary_range = SelectionRange::new(self.primary_range.page, start, end);
        self.additional_ranges.clear();
        self.forward = true;
    }
    
    /// Select line at position
    pub fn select_line_at(&mut self, text: &str, position: usize) {
        let chars: Vec<char> = text.chars().collect();
        
        // Find line start
        let mut start = position;
        while start > 0 && chars[start - 1] != '\n' {
            start -= 1;
        }
        
        // Find line end (include newline if present)
        let mut end = position;
        while end < chars.len() && chars[end] != '\n' {
            end += 1;
        }
        if end < chars.len() && chars[end] == '\n' {
            end += 1;
        }
        
        self.primary_range = SelectionRange::new(self.primary_range.page, start, end);
        self.additional_ranges.clear();
        self.forward = true;
    }
    
    /// Select all text
    pub fn select_all(&mut self, text_length: usize) {
        self.primary_range = SelectionRange::new(self.primary_range.page, 0, text_length);
        self.additional_ranges.clear();
        self.forward = true;
    }
    
    /// Check if position is selected
    pub fn contains_position(&self, position: usize) -> bool {
        self.ranges().any(|range| range.contains(position))
    }
    
    /// Get selected text from document
    pub fn get_text(&self, text: &str) -> String {
        let mut result = String::new();
        
        for range in self.ranges() {
            let chars: Vec<char> = text.chars().collect();
            let start = range.start().min(chars.len());
            let end = range.end().min(chars.len());
            
            let selected: String = chars[start..end].iter().collect();
            if !result.is_empty() {
                result.push('\n'); // Separate multiple selections
            }
            result.push_str(&selected);
        }
        
        result
    }
    
    /// Sort ranges by position
    fn sort_ranges(&mut self) {
        self.additional_ranges.sort_by_key(|r| r.start);
        
        // Ensure primary range is the first one
        if let Some(first_additional) = self.additional_ranges.first() {
            if first_additional.start < self.primary_range.start {
                let old_primary = self.primary_range.clone();
                self.primary_range = self.additional_ranges.remove(0);
                self.additional_ranges.insert(0, old_primary);
                self.sort_ranges(); // Re-sort after swap
            }
        }
    }
}

/// Selection builder for creating complex selections
#[derive(Debug)]
pub struct SelectionBuilder {
    page: u32,
    ranges: Vec<SelectionRange>,
}

impl SelectionBuilder {
    /// Create a new selection builder
    pub fn new(page: u32) -> Self {
        Self {
            page,
            ranges: Vec::new(),
        }
    }
    
    /// Add a range to the selection
    pub fn add_range(mut self, start: usize, end: usize) -> Self {
        self.ranges.push(SelectionRange::new(self.page, start, end));
        self
    }
    
    /// Add a word selection
    pub fn add_word(mut self, text: &str, position: usize) -> Self {
        let (start, end) = utils::find_word_boundaries(text, position);
        self.ranges.push(SelectionRange::new(self.page, start, end));
        self
    }
    
    /// Build the final selection
    pub fn build(self) -> Option<TextSelection> {
        if self.ranges.is_empty() {
            return None;
        }
        
        let mut selection = TextSelection::new(self.ranges[0].clone());
        
        for range in self.ranges.into_iter().skip(1) {
            selection.add_range(range);
        }
        
        Some(selection)
    }
}

/// Selection utilities
pub mod selection_utils {
    use super::*;
    
    /// Calculate visual bounds for selection ranges
    pub fn calculate_visual_bounds(
        ranges: &[SelectionRange],
        layout: &TextLayout,
    ) -> Vec<Rect> {
        let mut bounds = Vec::new();
        
        for range in ranges {
            // Calculate bounds for each line that the range spans
            for line in layout.lines() {
                if line.start_position < range.end && line.end_position > range.start {
                    let start_x = if range.start <= line.start_position {
                        line.bounds.x
                    } else {
                        // Calculate x position for start of selection in this line
                        line.bounds.x + (range.start - line.start_position) as f32 * 8.0 // Approximate
                    };
                    
                    let end_x = if range.end >= line.end_position {
                        line.bounds.x + line.bounds.width
                    } else {
                        // Calculate x position for end of selection in this line
                        line.bounds.x + (range.end - line.start_position) as f32 * 8.0 // Approximate
                    };
                    
                    bounds.push(Rect::new(
                        start_x,
                        line.bounds.y,
                        end_x - start_x,
                        line.bounds.height,
                    ));
                }
            }
        }
        
        bounds
    }
    
    /// Merge overlapping selection ranges
    pub fn merge_ranges(ranges: &[SelectionRange]) -> Vec<SelectionRange> {
        if ranges.is_empty() {
            return Vec::new();
        }
        
        let mut sorted_ranges = ranges.to_vec();
        sorted_ranges.sort_by_key(|r| r.start);
        
        let mut merged = Vec::new();
        let mut current = sorted_ranges[0].clone();
        
        for range in sorted_ranges.into_iter().skip(1) {
            if let Some(merged_range) = current.merge(&range) {
                current = merged_range;
            } else {
                merged.push(current);
                current = range;
            }
        }
        
        merged.push(current);
        merged
    }
}
