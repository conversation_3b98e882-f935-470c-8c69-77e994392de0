/// Typography engine for text layout and rendering
/// 
/// This module provides advanced typography features including
/// text shaping, glyph positioning, and layout calculations.

use super::*;
use crate::document::{EditablePage, TextFormat};
use crate::resources::FontManager;
use crate::types::{Point, Rect};

use std::sync::{Arc, Mutex};

/// Typography engine for text layout
#[derive(Debug)]
pub struct TypographyEngine {
    /// Font manager reference
    font_manager: Arc<Mutex<FontManager>>,
    /// Glyph cache
    glyph_cache: HashMap<String, GlyphInfo>,
    /// Text shaping cache
    shaping_cache: HashMap<String, Vec<GlyphInfo>>,
    /// Layout cache
    layout_cache: HashMap<String, TextLayout>,
}

impl TypographyEngine {
    /// Create a new typography engine
    pub fn new(font_manager: Arc<Mutex<FontManager>>) -> Self {
        Self {
            font_manager,
            glyph_cache: HashMap::new(),
            shaping_cache: HashMap::new(),
            layout_cache: HashMap::new(),
        }
    }
    
    /// Shape text into glyphs
    pub fn shape_text(
        &mut self,
        text: &str,
        format: &TextFormat,
    ) -> TextEditingResult<Vec<GlyphInfo>> {
        let cache_key = format!("{}_{:?}", text, format);
        
        if let Some(cached) = self.shaping_cache.get(&cache_key) {
            return Ok(cached.clone());
        }
        
        let glyphs = self.perform_text_shaping(text, format)?;
        self.shaping_cache.insert(cache_key, glyphs.clone());
        
        Ok(glyphs)
    }
    
    /// Layout text for a page
    pub fn layout_page_text(&self, page: &EditablePage) -> TextEditingResult<TextLayout> {
        let page_key = format!("page_{}", page.page_number());
        
        if let Some(cached) = self.layout_cache.get(&page_key) {
            return Ok(cached.clone());
        }
        
        // Extract text objects from page and create layout
        let layout = self.create_page_layout(page)?;
        
        Ok(layout)
    }
    
    /// Calculate text metrics
    pub fn calculate_text_metrics(
        &mut self,
        text: &str,
        format: &TextFormat,
    ) -> TextEditingResult<TextMetrics> {
        let glyphs = self.shape_text(text, format)?;
        
        let mut width = 0.0;
        let mut height = format.size;
        let mut ascent = format.size * 0.8; // Approximate
        let mut descent = format.size * 0.2; // Approximate
        
        for glyph in &glyphs {
            width += glyph.advance_width;
            height = height.max(glyph.bounds.height);
        }
        
        Ok(TextMetrics {
            width,
            height,
            ascent,
            descent,
            line_height: height * 1.2, // Default line spacing
            glyph_count: glyphs.len(),
        })
    }
    
    /// Calculate text width
    pub fn calculate_text_width(
        &mut self,
        text: &str,
        format: &TextFormat,
    ) -> TextEditingResult<f32> {
        let metrics = self.calculate_text_metrics(text, format)?;
        Ok(metrics.width)
    }
    
    /// Calculate text height
    pub fn calculate_text_height(
        &mut self,
        text: &str,
        format: &TextFormat,
    ) -> TextEditingResult<f32> {
        let metrics = self.calculate_text_metrics(text, format)?;
        Ok(metrics.height)
    }
    
    /// Find character position at point
    pub fn hit_test(
        &mut self,
        text: &str,
        format: &TextFormat,
        point: Point,
        bounds: Rect,
    ) -> TextEditingResult<usize> {
        let glyphs = self.shape_text(text, format)?;
        
        let mut current_x = bounds.x;
        
        for (i, glyph) in glyphs.iter().enumerate() {
            let glyph_center = current_x + glyph.advance_width / 2.0;
            
            if point.x <= glyph_center {
                return Ok(i);
            }
            
            current_x += glyph.advance_width;
        }
        
        // Point is after all glyphs
        Ok(text.chars().count())
    }
    
    /// Get character bounds at position
    pub fn get_character_bounds(
        &mut self,
        text: &str,
        format: &TextFormat,
        position: usize,
        text_bounds: Rect,
    ) -> TextEditingResult<Rect> {
        let glyphs = self.shape_text(text, format)?;
        
        if position >= glyphs.len() {
            // Position is at end of text
            let total_width: f32 = glyphs.iter().map(|g| g.advance_width).sum();
            return Ok(Rect::new(
                text_bounds.x + total_width,
                text_bounds.y,
                2.0, // Cursor width
                text_bounds.height,
            ));
        }
        
        let mut current_x = text_bounds.x;
        
        for (i, glyph) in glyphs.iter().enumerate() {
            if i == position {
                return Ok(Rect::new(
                    current_x,
                    text_bounds.y,
                    glyph.advance_width,
                    text_bounds.height,
                ));
            }
            current_x += glyph.advance_width;
        }
        
        Err(TextEditingError::InvalidPosition(position))
    }
    
    /// Clear caches
    pub fn clear_caches(&mut self) {
        self.glyph_cache.clear();
        self.shaping_cache.clear();
        self.layout_cache.clear();
    }
    
    /// Get cache statistics
    pub fn cache_stats(&self) -> TypographyCacheStats {
        TypographyCacheStats {
            glyph_cache_size: self.glyph_cache.len(),
            shaping_cache_size: self.shaping_cache.len(),
            layout_cache_size: self.layout_cache.len(),
        }
    }
    
    // Private helper methods
    
    fn perform_text_shaping(
        &mut self,
        text: &str,
        format: &TextFormat,
    ) -> TextEditingResult<Vec<GlyphInfo>> {
        let mut glyphs = Vec::new();
        
        // Get font metrics
        let font_manager = self.font_manager.lock().unwrap();
        let font_metrics = font_manager.get_font_metrics(&format.font_family, format.size)
            .map_err(|_| TextEditingError::FontNotFound(format.font_family.clone()))?;
        
        // Simple character-to-glyph mapping (real implementation would use HarfBuzz or similar)
        for (i, ch) in text.chars().enumerate() {
            let advance_width = font_metrics.get_char_width(ch);
            
            let glyph = GlyphInfo {
                character: ch,
                glyph_id: i as u32, // Simplified
                advance_width,
                bounds: Rect::new(0.0, 0.0, advance_width, format.size),
                offset: Point::new(0.0, 0.0),
            };
            
            glyphs.push(glyph);
        }
        
        Ok(glyphs)
    }
    
    fn create_page_layout(&self, page: &EditablePage) -> TextEditingResult<TextLayout> {
        // Extract text from page layers and create layout
        // This is a simplified implementation
        
        let mut lines = Vec::new();
        let mut text_blocks = Vec::new();
        
        // Create a simple single-line layout for now
        let line = LineInfo {
            line_number: 0,
            start_position: 0,
            end_position: 0,
            bounds: Rect::new(0.0, 0.0, 100.0, 20.0),
            baseline: 16.0,
            width: 100.0,
            height: 20.0,
        };
        
        lines.push(line);
        
        Ok(TextLayout {
            lines,
            text_blocks,
            total_width: 100.0,
            total_height: 20.0,
        })
    }
}

/// Glyph information
#[derive(Debug, Clone)]
pub struct GlyphInfo {
    /// Unicode character
    pub character: char,
    /// Glyph ID in font
    pub glyph_id: u32,
    /// Horizontal advance width
    pub advance_width: f32,
    /// Glyph bounds
    pub bounds: Rect,
    /// Offset from baseline
    pub offset: Point,
}

/// Text run with consistent formatting
#[derive(Debug, Clone)]
pub struct TextRun {
    /// Text content
    pub text: String,
    /// Text format
    pub format: TextFormat,
    /// Glyphs for this run
    pub glyphs: Vec<GlyphInfo>,
    /// Bounds of the run
    pub bounds: Rect,
}

impl TextRun {
    /// Create a new text run
    pub fn new(text: String, format: TextFormat) -> Self {
        Self {
            text,
            format,
            glyphs: Vec::new(),
            bounds: Rect::new(0.0, 0.0, 0.0, 0.0),
        }
    }
    
    /// Calculate run width
    pub fn width(&self) -> f32 {
        self.glyphs.iter().map(|g| g.advance_width).sum()
    }
    
    /// Calculate run height
    pub fn height(&self) -> f32 {
        self.format.size
    }
}

/// Text metrics
#[derive(Debug, Clone)]
pub struct TextMetrics {
    /// Total text width
    pub width: f32,
    /// Total text height
    pub height: f32,
    /// Ascent (above baseline)
    pub ascent: f32,
    /// Descent (below baseline)
    pub descent: f32,
    /// Line height (including spacing)
    pub line_height: f32,
    /// Number of glyphs
    pub glyph_count: usize,
}

/// Typography cache statistics
#[derive(Debug, Clone)]
pub struct TypographyCacheStats {
    pub glyph_cache_size: usize,
    pub shaping_cache_size: usize,
    pub layout_cache_size: usize,
}
