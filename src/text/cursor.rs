/// Text cursor management
/// 
/// This module provides cursor positioning and movement functionality
/// for text editing operations.

use super::*;
use crate::types::Point;

/// Text cursor for tracking editing position
#[derive(Debug, <PERSON>lone)]
pub struct TextCursor {
    /// Current page number (1-based)
    page: u32,
    /// Current character position in page text
    position: usize,
    /// Visual position on screen (x, y coordinates)
    visual_position: Point,
    /// Preferred column for vertical movement
    preferred_column: f32,
    /// Whether cursor is visible
    visible: bool,
    /// Cursor blink state
    blink_state: bool,
    /// Last blink time (for animation)
    last_blink_time: u64,
}

impl TextCursor {
    /// Create a new cursor at the beginning of page 1
    pub fn new() -> Self {
        Self {
            page: 1,
            position: 0,
            visual_position: Point::new(0.0, 0.0),
            preferred_column: 0.0,
            visible: true,
            blink_state: true,
            last_blink_time: 0,
        }
    }
    
    /// Create a cursor at a specific position
    pub fn at_position(page: u32, position: usize) -> Self {
        let mut cursor = Self::new();
        cursor.page = page;
        cursor.position = position;
        cursor
    }
    
    /// Get current page number
    pub fn page(&self) -> u32 {
        self.page
    }
    
    /// Get current character position
    pub fn position(&self) -> usize {
        self.position
    }
    
    /// Get visual position on screen
    pub fn visual_position(&self) -> Point {
        self.visual_position
    }
    
    /// Set visual position
    pub fn set_visual_position(&mut self, position: Point) {
        self.visual_position = position;
        self.preferred_column = position.x;
    }
    
    /// Get preferred column for vertical movement
    pub fn preferred_column(&self) -> f32 {
        self.preferred_column
    }
    
    /// Check if cursor is visible
    pub fn is_visible(&self) -> bool {
        self.visible
    }
    
    /// Set cursor visibility
    pub fn set_visible(&mut self, visible: bool) {
        self.visible = visible;
    }
    
    /// Get cursor blink state
    pub fn blink_state(&self) -> bool {
        self.blink_state
    }
    
    /// Update cursor blink state
    pub fn update_blink(&mut self, current_time: u64) {
        const BLINK_INTERVAL: u64 = 500; // 500ms
        
        if current_time - self.last_blink_time > BLINK_INTERVAL {
            self.blink_state = !self.blink_state;
            self.last_blink_time = current_time;
        }
    }
    
    /// Move cursor to specific position
    pub fn move_to(&mut self, page: u32, position: usize) {
        self.page = page;
        self.position = position;
        self.reset_blink();
    }
    
    /// Move cursor by character count
    pub fn move_by(&mut self, count: usize) {
        self.position += count;
        self.reset_blink();
    }
    
    /// Move cursor by signed offset
    pub fn move_by_signed(&mut self, offset: isize) {
        if offset < 0 {
            let abs_offset = (-offset) as usize;
            if abs_offset <= self.position {
                self.position -= abs_offset;
            } else {
                self.position = 0;
            }
        } else {
            self.position += offset as usize;
        }
        self.reset_blink();
    }
    
    /// Move cursor to beginning of line
    pub fn move_to_line_start(&mut self, text: &str) {
        let line_start = self.find_line_start(text);
        self.position = line_start;
        self.reset_blink();
    }
    
    /// Move cursor to end of line
    pub fn move_to_line_end(&mut self, text: &str) {
        let line_end = self.find_line_end(text);
        self.position = line_end;
        self.reset_blink();
    }
    
    /// Move cursor up one line
    pub fn move_up(&mut self, text: &str, layout: &TextLayout) -> TextEditingResult<()> {
        let current_line = self.find_current_line(layout)?;
        
        if current_line > 0 {
            let target_line = current_line - 1;
            let new_position = self.find_position_in_line(layout, target_line, self.preferred_column)?;
            self.position = new_position;
            self.reset_blink();
        }
        
        Ok(())
    }
    
    /// Move cursor down one line
    pub fn move_down(&mut self, text: &str, layout: &TextLayout) -> TextEditingResult<()> {
        let current_line = self.find_current_line(layout)?;
        
        if current_line < layout.line_count() - 1 {
            let target_line = current_line + 1;
            let new_position = self.find_position_in_line(layout, target_line, self.preferred_column)?;
            self.position = new_position;
            self.reset_blink();
        }
        
        Ok(())
    }
    
    /// Move cursor to beginning of word
    pub fn move_to_word_start(&mut self, text: &str) {
        let (word_start, _) = utils::find_word_boundaries(text, self.position);
        self.position = word_start;
        self.reset_blink();
    }
    
    /// Move cursor to end of word
    pub fn move_to_word_end(&mut self, text: &str) {
        let (_, word_end) = utils::find_word_boundaries(text, self.position);
        self.position = word_end;
        self.reset_blink();
    }
    
    /// Move cursor to previous word
    pub fn move_to_previous_word(&mut self, text: &str) {
        if self.position > 0 {
            // Skip current word if we're in the middle of it
            let mut pos = self.position;
            let chars: Vec<char> = text.chars().collect();
            
            // Skip whitespace
            while pos > 0 && chars[pos - 1].is_whitespace() {
                pos -= 1;
            }
            
            // Skip word characters
            while pos > 0 && chars[pos - 1].is_alphanumeric() {
                pos -= 1;
            }
            
            self.position = pos;
            self.reset_blink();
        }
    }
    
    /// Move cursor to next word
    pub fn move_to_next_word(&mut self, text: &str) {
        let chars: Vec<char> = text.chars().collect();
        let mut pos = self.position;
        
        // Skip current word
        while pos < chars.len() && chars[pos].is_alphanumeric() {
            pos += 1;
        }
        
        // Skip whitespace
        while pos < chars.len() && chars[pos].is_whitespace() {
            pos += 1;
        }
        
        self.position = pos;
        self.reset_blink();
    }
    
    /// Move cursor to beginning of document
    pub fn move_to_document_start(&mut self) {
        self.page = 1;
        self.position = 0;
        self.reset_blink();
    }
    
    /// Move cursor to end of document
    pub fn move_to_document_end(&mut self, document_length: usize) {
        self.position = document_length;
        self.reset_blink();
    }
    
    /// Reset cursor blink state
    fn reset_blink(&mut self) {
        self.blink_state = true;
        self.last_blink_time = 0;
    }
    
    /// Find the start of the current line
    fn find_line_start(&self, text: &str) -> usize {
        let chars: Vec<char> = text.chars().collect();
        let mut pos = self.position;
        
        while pos > 0 && chars[pos - 1] != '\n' {
            pos -= 1;
        }
        
        pos
    }
    
    /// Find the end of the current line
    fn find_line_end(&self, text: &str) -> usize {
        let chars: Vec<char> = text.chars().collect();
        let mut pos = self.position;
        
        while pos < chars.len() && chars[pos] != '\n' {
            pos += 1;
        }
        
        pos
    }
    
    /// Find the current line number in layout
    fn find_current_line(&self, layout: &TextLayout) -> TextEditingResult<usize> {
        for (line_index, line) in layout.lines().iter().enumerate() {
            if self.position >= line.start_position && self.position <= line.end_position {
                return Ok(line_index);
            }
        }
        
        // If not found, return last line
        Ok(layout.line_count().saturating_sub(1))
    }
    
    /// Find position in a specific line at the preferred column
    fn find_position_in_line(
        &self,
        layout: &TextLayout,
        line_index: usize,
        column: f32,
    ) -> TextEditingResult<usize> {
        if line_index >= layout.line_count() {
            return Err(TextEditingError::InvalidPosition(line_index));
        }
        
        let line = &layout.lines()[line_index];
        
        // Find the character position closest to the preferred column
        // This would require glyph positioning information from the typography engine
        // For now, we'll use a simple approximation
        
        let line_length = line.end_position - line.start_position;
        let relative_position = (column / line.width * line_length as f32) as usize;
        
        Ok(line.start_position + relative_position.min(line_length))
    }
}

impl Default for TextCursor {
    fn default() -> Self {
        Self::new()
    }
}

/// Cursor position information
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub struct CursorPosition {
    /// Page number
    pub page: u32,
    /// Character position
    pub position: usize,
    /// Line number (0-based)
    pub line: usize,
    /// Column number (0-based)
    pub column: usize,
}

impl CursorPosition {
    /// Create a new cursor position
    pub fn new(page: u32, position: usize, line: usize, column: usize) -> Self {
        Self {
            page,
            position,
            line,
            column,
        }
    }
    
    /// Create from cursor and text layout
    pub fn from_cursor(cursor: &TextCursor, layout: &TextLayout) -> TextEditingResult<Self> {
        let line = cursor.find_current_line(layout)?;
        let line_info = &layout.lines()[line];
        let column = cursor.position() - line_info.start_position;
        
        Ok(Self::new(cursor.page(), cursor.position(), line, column))
    }
}
