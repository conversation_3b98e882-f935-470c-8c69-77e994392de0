/// Text editing engine
/// 
/// This module provides comprehensive text editing capabilities including
/// text selection, cursor management, formatting, and text manipulation.

pub mod text_engine;
pub mod text_selection;
pub mod cursor;
pub mod typography;
pub mod text_layout;

pub use text_engine::TextEngine;
pub use text_selection::{TextSelection, SelectionRange};
pub use cursor::{TextCursor, CursorPosition};
pub use typography::{TypographyEngine, TextRun, GlyphInfo};
pub use text_layout::{TextLayout, LineInfo, TextBlock};

use crate::document::{TextFormat, Color, FontWeight, FontStyle};
use crate::types::{Point, Rect};
use crate::error::{PDFError, PDFResult};
use crate::resources::FontManager;

use std::collections::HashMap;
use serde::{Deserialize, Serialize};

/// Text editing capabilities
#[derive(Debug, Clone)]
pub struct TextEditingCapabilities {
    /// Support for rich text formatting
    pub rich_text: bool,
    /// Support for multiple fonts
    pub multiple_fonts: bool,
    /// Support for text selection
    pub text_selection: bool,
    /// Support for find and replace
    pub find_replace: bool,
    /// Support for spell checking
    pub spell_check: bool,
    /// Support for text wrapping
    pub text_wrapping: bool,
    /// Support for text alignment
    pub text_alignment: bool,
    /// Support for line spacing
    pub line_spacing: bool,
    /// Support for paragraph formatting
    pub paragraph_formatting: bool,
}

impl Default for TextEditingCapabilities {
    fn default() -> Self {
        Self {
            rich_text: true,
            multiple_fonts: true,
            text_selection: true,
            find_replace: true,
            spell_check: false, // Requires external library
            text_wrapping: true,
            text_alignment: true,
            line_spacing: true,
            paragraph_formatting: true,
        }
    }
}

/// Text editing mode
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum TextEditingMode {
    /// Insert mode - new text is inserted at cursor
    Insert,
    /// Overwrite mode - new text replaces existing text
    Overwrite,
    /// Selection mode - text can be selected
    Selection,
}

/// Text alignment options
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum TextAlignment {
    Left,
    Center,
    Right,
    Justify,
}

/// Line spacing options
#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
pub enum LineSpacing {
    Single,
    OneAndHalf,
    Double,
    Custom(f32),
}

/// Paragraph formatting
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ParagraphFormat {
    /// Text alignment
    pub alignment: TextAlignment,
    /// Line spacing
    pub line_spacing: LineSpacing,
    /// Left indent in points
    pub left_indent: f32,
    /// Right indent in points
    pub right_indent: f32,
    /// First line indent in points
    pub first_line_indent: f32,
    /// Space before paragraph in points
    pub space_before: f32,
    /// Space after paragraph in points
    pub space_after: f32,
}

impl Default for ParagraphFormat {
    fn default() -> Self {
        Self {
            alignment: TextAlignment::Left,
            line_spacing: LineSpacing::Single,
            left_indent: 0.0,
            right_indent: 0.0,
            first_line_indent: 0.0,
            space_before: 0.0,
            space_after: 0.0,
        }
    }
}

/// Text search options
#[derive(Debug, Clone)]
pub struct TextSearchOptions {
    /// Case sensitive search
    pub case_sensitive: bool,
    /// Whole word matching
    pub whole_word: bool,
    /// Regular expression search
    pub regex: bool,
    /// Search direction (true = forward, false = backward)
    pub forward: bool,
    /// Wrap around when reaching end/beginning
    pub wrap_around: bool,
}

impl Default for TextSearchOptions {
    fn default() -> Self {
        Self {
            case_sensitive: false,
            whole_word: false,
            regex: false,
            forward: true,
            wrap_around: true,
        }
    }
}

/// Text search result
#[derive(Debug, Clone)]
pub struct TextSearchResult {
    /// Page number where text was found
    pub page: u32,
    /// Start position in page text
    pub start_position: usize,
    /// End position in page text
    pub end_position: usize,
    /// Bounding rectangle of found text
    pub bounds: Rect,
    /// The matched text
    pub matched_text: String,
}

/// Text replacement operation
#[derive(Debug, Clone)]
pub struct TextReplacement {
    /// Search pattern
    pub search: String,
    /// Replacement text
    pub replace: String,
    /// Search options
    pub options: TextSearchOptions,
    /// Replace all occurrences
    pub replace_all: bool,
}

/// Text editing statistics
#[derive(Debug, Clone, Default)]
pub struct TextEditingStats {
    /// Number of characters inserted
    pub characters_inserted: usize,
    /// Number of characters deleted
    pub characters_deleted: usize,
    /// Number of formatting operations
    pub formatting_operations: usize,
    /// Number of search operations
    pub search_operations: usize,
    /// Number of replace operations
    pub replace_operations: usize,
}

/// Text editing error types
#[derive(Debug, Clone)]
pub enum TextEditingError {
    /// Invalid text position
    InvalidPosition(usize),
    /// Invalid selection range
    InvalidSelection(usize, usize),
    /// Font not found
    FontNotFound(String),
    /// Text too long
    TextTooLong(usize),
    /// Invalid formatting
    InvalidFormatting(String),
    /// Search pattern error
    SearchError(String),
}

impl std::fmt::Display for TextEditingError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            TextEditingError::InvalidPosition(pos) => {
                write!(f, "Invalid text position: {}", pos)
            }
            TextEditingError::InvalidSelection(start, end) => {
                write!(f, "Invalid selection range: {} to {}", start, end)
            }
            TextEditingError::FontNotFound(font) => {
                write!(f, "Font not found: {}", font)
            }
            TextEditingError::TextTooLong(len) => {
                write!(f, "Text too long: {} characters", len)
            }
            TextEditingError::InvalidFormatting(msg) => {
                write!(f, "Invalid formatting: {}", msg)
            }
            TextEditingError::SearchError(msg) => {
                write!(f, "Search error: {}", msg)
            }
        }
    }
}

impl std::error::Error for TextEditingError {}

/// Convert TextEditingError to PDFError
impl From<TextEditingError> for PDFError {
    fn from(err: TextEditingError) -> Self {
        PDFError::ConfigError(err.to_string())
    }
}

/// Text editing result type
pub type TextEditingResult<T> = Result<T, TextEditingError>;

/// Text editing utilities
pub mod utils {
    use super::*;
    
    /// Validate text position
    pub fn validate_position(text: &str, position: usize) -> TextEditingResult<()> {
        if position > text.len() {
            Err(TextEditingError::InvalidPosition(position))
        } else {
            Ok(())
        }
    }
    
    /// Validate selection range
    pub fn validate_selection(text: &str, start: usize, end: usize) -> TextEditingResult<()> {
        if start > text.len() || end > text.len() || start > end {
            Err(TextEditingError::InvalidSelection(start, end))
        } else {
            Ok(())
        }
    }
    
    /// Calculate character count
    pub fn character_count(text: &str) -> usize {
        text.chars().count()
    }
    
    /// Calculate word count
    pub fn word_count(text: &str) -> usize {
        text.split_whitespace().count()
    }
    
    /// Calculate line count
    pub fn line_count(text: &str) -> usize {
        text.lines().count().max(1)
    }
    
    /// Find word boundaries
    pub fn find_word_boundaries(text: &str, position: usize) -> (usize, usize) {
        let chars: Vec<char> = text.chars().collect();
        
        if chars.is_empty() || position >= chars.len() {
            return (position, position);
        }
        
        // Find start of word
        let mut start = position;
        while start > 0 && chars[start - 1].is_alphanumeric() {
            start -= 1;
        }
        
        // Find end of word
        let mut end = position;
        while end < chars.len() && chars[end].is_alphanumeric() {
            end += 1;
        }
        
        (start, end)
    }
    
    /// Convert byte position to character position
    pub fn byte_to_char_position(text: &str, byte_pos: usize) -> usize {
        text.char_indices()
            .take_while(|(i, _)| *i < byte_pos)
            .count()
    }
    
    /// Convert character position to byte position
    pub fn char_to_byte_position(text: &str, char_pos: usize) -> usize {
        text.char_indices()
            .nth(char_pos)
            .map(|(i, _)| i)
            .unwrap_or(text.len())
    }
}
