/// Text layout engine
/// 
/// This module provides text layout functionality including
/// line breaking, paragraph formatting, and text positioning.

use super::*;
use crate::types::{Point, Rect};

/// Text layout information
#[derive(Debug, Clone)]
pub struct TextLayout {
    /// Lines in the layout
    pub lines: Vec<LineInfo>,
    /// Text blocks (paragraphs)
    pub text_blocks: Vec<TextBlock>,
    /// Total layout width
    pub total_width: f32,
    /// Total layout height
    pub total_height: f32,
}

impl TextLayout {
    /// Create a new empty text layout
    pub fn new() -> Self {
        Self {
            lines: Vec::new(),
            text_blocks: Vec::new(),
            total_width: 0.0,
            total_height: 0.0,
        }
    }
    
    /// Get lines
    pub fn lines(&self) -> &[LineInfo] {
        &self.lines
    }
    
    /// Get text blocks
    pub fn text_blocks(&self) -> &[TextBlock] {
        &self.text_blocks
    }
    
    /// Get line count
    pub fn line_count(&self) -> usize {
        self.lines.len()
    }
    
    /// Get block count
    pub fn block_count(&self) -> usize {
        self.text_blocks.len()
    }
    
    /// Find line at position
    pub fn find_line_at_position(&self, position: usize) -> Option<&LineInfo> {
        self.lines.iter().find(|line| {
            position >= line.start_position && position <= line.end_position
        })
    }
    
    /// Find line at point
    pub fn find_line_at_point(&self, point: Point) -> Option<&LineInfo> {
        self.lines.iter().find(|line| {
            point.y >= line.bounds.y && point.y <= line.bounds.y + line.bounds.height
        })
    }
    
    /// Get line by index
    pub fn get_line(&self, index: usize) -> Option<&LineInfo> {
        self.lines.get(index)
    }
    
    /// Get block by index
    pub fn get_block(&self, index: usize) -> Option<&TextBlock> {
        self.text_blocks.get(index)
    }
    
    /// Add a line to the layout
    pub fn add_line(&mut self, line: LineInfo) {
        self.total_width = self.total_width.max(line.bounds.x + line.bounds.width);
        self.total_height = self.total_height.max(line.bounds.y + line.bounds.height);
        self.lines.push(line);
    }
    
    /// Add a text block to the layout
    pub fn add_block(&mut self, block: TextBlock) {
        self.total_width = self.total_width.max(block.bounds.x + block.bounds.width);
        self.total_height = self.total_height.max(block.bounds.y + block.bounds.height);
        self.text_blocks.push(block);
    }
    
    /// Calculate layout bounds
    pub fn bounds(&self) -> Rect {
        Rect::new(0.0, 0.0, self.total_width, self.total_height)
    }
    
    /// Update layout after text changes
    pub fn update_layout(&mut self, text: &str, format: &TextFormat, bounds: Rect) {
        self.lines.clear();
        self.text_blocks.clear();
        self.total_width = 0.0;
        self.total_height = 0.0;
        
        // Perform layout
        self.layout_text(text, format, bounds);
    }
    
    /// Perform text layout
    fn layout_text(&mut self, text: &str, format: &TextFormat, bounds: Rect) {
        let mut current_y = bounds.y;
        let mut current_position = 0;
        
        // Split text into paragraphs
        let paragraphs: Vec<&str> = text.split('\n').collect();
        
        for (para_index, paragraph) in paragraphs.iter().enumerate() {
            let block_start_y = current_y;
            let block_start_position = current_position;
            
            // Layout paragraph
            let (lines, block_height) = self.layout_paragraph(
                paragraph,
                format,
                Rect::new(bounds.x, current_y, bounds.width, bounds.height - (current_y - bounds.y)),
                current_position,
            );
            
            // Add lines to layout
            for line in lines {
                self.add_line(line);
            }
            
            // Create text block
            let block = TextBlock {
                block_index: para_index,
                start_position: block_start_position,
                end_position: current_position + paragraph.chars().count(),
                bounds: Rect::new(bounds.x, block_start_y, bounds.width, block_height),
                paragraph_format: ParagraphFormat::default(),
                text_runs: vec![TextRun::new(paragraph.to_string(), format.clone())],
            };
            
            self.add_block(block);
            
            current_y += block_height;
            current_position += paragraph.chars().count() + 1; // +1 for newline
        }
    }
    
    /// Layout a single paragraph
    fn layout_paragraph(
        &self,
        text: &str,
        format: &TextFormat,
        bounds: Rect,
        start_position: usize,
    ) -> (Vec<LineInfo>, f32) {
        let mut lines = Vec::new();
        let mut current_y = bounds.y;
        let mut current_position = start_position;
        
        let line_height = format.size * 1.2; // Default line spacing
        
        // Simple word wrapping
        let words: Vec<&str> = text.split_whitespace().collect();
        let mut current_line = String::new();
        let mut line_start_position = current_position;
        
        for word in words {
            let test_line = if current_line.is_empty() {
                word.to_string()
            } else {
                format!("{} {}", current_line, word)
            };
            
            // Estimate line width (simplified)
            let estimated_width = test_line.len() as f32 * format.size * 0.6;
            
            if estimated_width <= bounds.width || current_line.is_empty() {
                current_line = test_line;
            } else {
                // Create line with current content
                let line = LineInfo {
                    line_number: lines.len(),
                    start_position: line_start_position,
                    end_position: current_position,
                    bounds: Rect::new(bounds.x, current_y, estimated_width, line_height),
                    baseline: current_y + line_height * 0.8,
                    width: estimated_width,
                    height: line_height,
                };
                
                lines.push(line);
                
                // Start new line
                current_line = word.to_string();
                current_y += line_height;
                line_start_position = current_position;
            }
            
            current_position += word.chars().count() + 1; // +1 for space
        }
        
        // Add final line if not empty
        if !current_line.is_empty() {
            let estimated_width = current_line.len() as f32 * format.size * 0.6;
            let line = LineInfo {
                line_number: lines.len(),
                start_position: line_start_position,
                end_position: current_position - 1, // -1 to remove extra space
                bounds: Rect::new(bounds.x, current_y, estimated_width, line_height),
                baseline: current_y + line_height * 0.8,
                width: estimated_width,
                height: line_height,
            };
            
            lines.push(line);
            current_y += line_height;
        }
        
        let total_height = current_y - bounds.y;
        (lines, total_height)
    }
}

impl Default for TextLayout {
    fn default() -> Self {
        Self::new()
    }
}

/// Information about a single line of text
#[derive(Debug, Clone)]
pub struct LineInfo {
    /// Line number (0-based)
    pub line_number: usize,
    /// Start character position in text
    pub start_position: usize,
    /// End character position in text
    pub end_position: usize,
    /// Line bounds
    pub bounds: Rect,
    /// Baseline Y position
    pub baseline: f32,
    /// Line width
    pub width: f32,
    /// Line height
    pub height: f32,
}

impl LineInfo {
    /// Create a new line info
    pub fn new(
        line_number: usize,
        start_position: usize,
        end_position: usize,
        bounds: Rect,
    ) -> Self {
        Self {
            line_number,
            start_position,
            end_position,
            bounds,
            baseline: bounds.y + bounds.height * 0.8,
            width: bounds.width,
            height: bounds.height,
        }
    }
    
    /// Get line length in characters
    pub fn length(&self) -> usize {
        self.end_position - self.start_position
    }
    
    /// Check if position is in this line
    pub fn contains_position(&self, position: usize) -> bool {
        position >= self.start_position && position <= self.end_position
    }
    
    /// Check if point is in this line
    pub fn contains_point(&self, point: Point) -> bool {
        self.bounds.contains_point(point)
    }
}

/// Text block (paragraph) information
#[derive(Debug, Clone)]
pub struct TextBlock {
    /// Block index
    pub block_index: usize,
    /// Start character position
    pub start_position: usize,
    /// End character position
    pub end_position: usize,
    /// Block bounds
    pub bounds: Rect,
    /// Paragraph formatting
    pub paragraph_format: ParagraphFormat,
    /// Text runs in this block
    pub text_runs: Vec<TextRun>,
}

impl TextBlock {
    /// Create a new text block
    pub fn new(
        block_index: usize,
        start_position: usize,
        end_position: usize,
        bounds: Rect,
    ) -> Self {
        Self {
            block_index,
            start_position,
            end_position,
            bounds,
            paragraph_format: ParagraphFormat::default(),
            text_runs: Vec::new(),
        }
    }
    
    /// Get block length in characters
    pub fn length(&self) -> usize {
        self.end_position - self.start_position
    }
    
    /// Check if position is in this block
    pub fn contains_position(&self, position: usize) -> bool {
        position >= self.start_position && position <= self.end_position
    }
    
    /// Add a text run to this block
    pub fn add_run(&mut self, run: TextRun) {
        self.text_runs.push(run);
    }
    
    /// Get all text in this block
    pub fn get_text(&self) -> String {
        self.text_runs.iter().map(|run| &run.text).collect()
    }
    
    /// Apply paragraph formatting
    pub fn apply_formatting(&mut self, format: ParagraphFormat) {
        self.paragraph_format = format;
        // Update layout based on new formatting
        self.update_layout();
    }
    
    /// Update layout after formatting changes
    fn update_layout(&mut self) {
        // Recalculate bounds and positions based on paragraph format
        // This would involve adjusting indentation, alignment, spacing, etc.
    }
}

/// Layout builder for creating complex layouts
#[derive(Debug)]
pub struct LayoutBuilder {
    layout: TextLayout,
    current_position: usize,
    current_y: f32,
    bounds: Rect,
}

impl LayoutBuilder {
    /// Create a new layout builder
    pub fn new(bounds: Rect) -> Self {
        Self {
            layout: TextLayout::new(),
            current_position: 0,
            current_y: bounds.y,
            bounds,
        }
    }
    
    /// Add text with formatting
    pub fn add_text(mut self, text: &str, format: &TextFormat) -> Self {
        // Layout the text and add to layout
        let (lines, height) = self.layout.layout_paragraph(
            text,
            format,
            Rect::new(self.bounds.x, self.current_y, self.bounds.width, self.bounds.height - (self.current_y - self.bounds.y)),
            self.current_position,
        );
        
        for line in lines {
            self.layout.add_line(line);
        }
        
        self.current_y += height;
        self.current_position += text.chars().count();
        
        self
    }
    
    /// Add a line break
    pub fn add_line_break(mut self, height: f32) -> Self {
        self.current_y += height;
        self
    }
    
    /// Build the final layout
    pub fn build(self) -> TextLayout {
        self.layout
    }
}

/// Layout utilities
pub mod layout_utils {
    use super::*;
    
    /// Calculate optimal line breaks for text
    pub fn calculate_line_breaks(
        text: &str,
        format: &TextFormat,
        max_width: f32,
    ) -> Vec<usize> {
        let mut breaks = Vec::new();
        let words: Vec<&str> = text.split_whitespace().collect();
        let mut current_width = 0.0;
        let mut current_position = 0;
        
        for word in words {
            let word_width = word.len() as f32 * format.size * 0.6; // Simplified
            
            if current_width + word_width > max_width && current_width > 0.0 {
                breaks.push(current_position);
                current_width = word_width;
            } else {
                current_width += word_width + format.size * 0.3; // Space width
            }
            
            current_position += word.chars().count() + 1;
        }
        
        breaks
    }
    
    /// Apply text alignment to lines
    pub fn apply_alignment(lines: &mut [LineInfo], alignment: TextAlignment, container_width: f32) {
        for line in lines {
            match alignment {
                TextAlignment::Left => {
                    // Already left-aligned
                }
                TextAlignment::Center => {
                    let offset = (container_width - line.width) / 2.0;
                    line.bounds.x += offset;
                }
                TextAlignment::Right => {
                    let offset = container_width - line.width;
                    line.bounds.x += offset;
                }
                TextAlignment::Justify => {
                    // Justify would require adjusting spacing between words
                    // This is a complex operation that would be implemented
                    // in a full typography engine
                }
            }
        }
    }
}
