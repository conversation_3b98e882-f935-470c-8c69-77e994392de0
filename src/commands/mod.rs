/// Command pattern implementation for PDF editing operations
/// 
/// This module provides a comprehensive command pattern implementation
/// that enables undo/redo functionality and operation batching.

pub mod base;
pub mod command_manager;
pub mod text;
pub mod pages;
pub mod graphics;
pub mod annotations;

pub use base::{Command, CommandResult, CommandError, ExecutionContext};
pub use command_manager::{CommandManager, CommandBatch, BatchOptions};

use crate::document::{EditableDocument, DocumentChange, ChangeType, ChangeState};
use crate::error::{PDFError, PDFResult};
use serde::{Deserialize, Serialize};

/// Command execution result
pub type CommandResult<T = ()> = Result<T, CommandError>;

/// Command error types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum CommandError {
    /// Permission denied
    PermissionDenied(String),
    /// Invalid parameters
    InvalidParameters(String),
    /// Document state error
    DocumentState(String),
    /// Resource not found
    ResourceNotFound(String),
    /// Validation failed
    ValidationFailed(String),
    /// Execution failed
    ExecutionFailed(String),
    /// Undo/redo not possible
    UndoRedoFailed(String),
}

impl std::fmt::Display for CommandError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            CommandError::PermissionDenied(msg) => write!(f, "Permission denied: {}", msg),
            CommandError::InvalidParameters(msg) => write!(f, "Invalid parameters: {}", msg),
            CommandError::DocumentState(msg) => write!(f, "Document state error: {}", msg),
            CommandError::ResourceNotFound(msg) => write!(f, "Resource not found: {}", msg),
            CommandError::ValidationFailed(msg) => write!(f, "Validation failed: {}", msg),
            CommandError::ExecutionFailed(msg) => write!(f, "Execution failed: {}", msg),
            CommandError::UndoRedoFailed(msg) => write!(f, "Undo/redo failed: {}", msg),
        }
    }
}

impl std::error::Error for CommandError {}

impl From<CommandError> for PDFError {
    fn from(err: CommandError) -> Self {
        PDFError::ConfigError(err.to_string())
    }
}

/// Command priority levels
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord)]
pub enum CommandPriority {
    Low = 0,
    Normal = 1,
    High = 2,
    Critical = 3,
}

/// Command execution mode
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ExecutionMode {
    /// Execute immediately
    Immediate,
    /// Queue for batch execution
    Queued,
    /// Execute asynchronously
    Async,
}

/// Command metadata
#[derive(Debug, Clone)]
pub struct CommandMetadata {
    /// Command name/type
    pub name: String,
    /// Command description
    pub description: String,
    /// Command priority
    pub priority: CommandPriority,
    /// Execution mode
    pub execution_mode: ExecutionMode,
    /// Whether the command can be undone
    pub undoable: bool,
    /// Whether the command modifies the document
    pub modifies_document: bool,
    /// Estimated execution time in milliseconds
    pub estimated_duration: Option<u32>,
}

impl CommandMetadata {
    pub fn new(name: String, description: String) -> Self {
        Self {
            name,
            description,
            priority: CommandPriority::Normal,
            execution_mode: ExecutionMode::Immediate,
            undoable: true,
            modifies_document: true,
            estimated_duration: None,
        }
    }
    
    pub fn with_priority(mut self, priority: CommandPriority) -> Self {
        self.priority = priority;
        self
    }
    
    pub fn with_execution_mode(mut self, mode: ExecutionMode) -> Self {
        self.execution_mode = mode;
        self
    }
    
    pub fn non_undoable(mut self) -> Self {
        self.undoable = false;
        self
    }
    
    pub fn read_only(mut self) -> Self {
        self.modifies_document = false;
        self
    }
    
    pub fn with_duration(mut self, duration_ms: u32) -> Self {
        self.estimated_duration = Some(duration_ms);
        self
    }
}

/// Command validation result
#[derive(Debug, Clone)]
pub struct ValidationResult {
    pub is_valid: bool,
    pub errors: Vec<String>,
    pub warnings: Vec<String>,
}

impl ValidationResult {
    pub fn valid() -> Self {
        Self {
            is_valid: true,
            errors: Vec::new(),
            warnings: Vec::new(),
        }
    }
    
    pub fn invalid(error: String) -> Self {
        Self {
            is_valid: false,
            errors: vec![error],
            warnings: Vec::new(),
        }
    }
    
    pub fn with_warning(mut self, warning: String) -> Self {
        self.warnings.push(warning);
        self
    }
    
    pub fn add_error(&mut self, error: String) {
        self.errors.push(error);
        self.is_valid = false;
    }
    
    pub fn add_warning(&mut self, warning: String) {
        self.warnings.push(warning);
    }
}

/// Command execution statistics
#[derive(Debug, Clone, Default)]
pub struct ExecutionStats {
    /// Number of commands executed
    pub commands_executed: u32,
    /// Number of successful executions
    pub successful_executions: u32,
    /// Number of failed executions
    pub failed_executions: u32,
    /// Number of undo operations
    pub undo_operations: u32,
    /// Number of redo operations
    pub redo_operations: u32,
    /// Total execution time in milliseconds
    pub total_execution_time: u64,
    /// Average execution time in milliseconds
    pub average_execution_time: f64,
}

impl ExecutionStats {
    pub fn record_execution(&mut self, duration_ms: u64, success: bool) {
        self.commands_executed += 1;
        self.total_execution_time += duration_ms;
        
        if success {
            self.successful_executions += 1;
        } else {
            self.failed_executions += 1;
        }
        
        self.average_execution_time = self.total_execution_time as f64 / self.commands_executed as f64;
    }
    
    pub fn record_undo(&mut self) {
        self.undo_operations += 1;
    }
    
    pub fn record_redo(&mut self) {
        self.redo_operations += 1;
    }
    
    pub fn success_rate(&self) -> f64 {
        if self.commands_executed == 0 {
            0.0
        } else {
            self.successful_executions as f64 / self.commands_executed as f64
        }
    }
}

/// Command factory for creating common commands
pub struct CommandFactory;

impl CommandFactory {
    /// Create a text insertion command
    pub fn insert_text(
        page: u32,
        position: usize,
        text: String,
        format: Option<crate::document::TextFormat>,
    ) -> Box<dyn Command> {
        Box::new(text::InsertTextCommand::new(page, position, text, format))
    }
    
    /// Create a text deletion command
    pub fn delete_text(
        page: u32,
        start: usize,
        end: usize,
    ) -> Box<dyn Command> {
        Box::new(text::DeleteTextCommand::new(page, start, end))
    }
    
    /// Create a page insertion command
    pub fn insert_page(
        position: u32,
        settings: Option<crate::document::PageSettings>,
    ) -> Box<dyn Command> {
        Box::new(pages::InsertPageCommand::new(position, settings))
    }
    
    /// Create a page deletion command
    pub fn delete_page(page: u32) -> Box<dyn Command> {
        Box::new(pages::DeletePageCommand::new(page))
    }
    
    /// Create a page move command
    pub fn move_page(from_page: u32, to_position: u32) -> Box<dyn Command> {
        Box::new(pages::MovePageCommand::new(from_page, to_position))
    }
}

/// Macro for creating command metadata
#[macro_export]
macro_rules! command_metadata {
    ($name:expr, $description:expr) => {
        CommandMetadata::new($name.to_string(), $description.to_string())
    };
    ($name:expr, $description:expr, priority: $priority:expr) => {
        CommandMetadata::new($name.to_string(), $description.to_string())
            .with_priority($priority)
    };
    ($name:expr, $description:expr, mode: $mode:expr) => {
        CommandMetadata::new($name.to_string(), $description.to_string())
            .with_execution_mode($mode)
    };
    ($name:expr, $description:expr, priority: $priority:expr, mode: $mode:expr) => {
        CommandMetadata::new($name.to_string(), $description.to_string())
            .with_priority($priority)
            .with_execution_mode($mode)
    };
}

/// Utility functions for command operations
pub mod utils {
    use super::*;
    
    /// Generate a unique command ID
    pub fn generate_command_id() -> String {
        format!("cmd_{}", js_sys::Date::now() as u64)
    }
    
    /// Validate command parameters
    pub fn validate_page_number(page: u32, document: &EditableDocument) -> CommandResult<()> {
        if page == 0 || page > document.page_count() {
            return Err(CommandError::InvalidParameters(
                format!("Invalid page number: {} (document has {} pages)", page, document.page_count())
            ));
        }
        Ok(())
    }
    
    /// Validate text position
    pub fn validate_text_position(position: usize, text_length: usize) -> CommandResult<()> {
        if position > text_length {
            return Err(CommandError::InvalidParameters(
                format!("Invalid text position: {} (text length: {})", position, text_length)
            ));
        }
        Ok(())
    }
    
    /// Check editing permissions
    pub fn check_permission(
        document: &EditableDocument,
        required_permission: &str,
    ) -> CommandResult<()> {
        // This would check against document permissions
        // For now, we'll implement basic checks
        match required_permission {
            "edit_text" => {
                if !document.metadata().editing.modification_count > 0 {
                    // Basic permission check - in real implementation,
                    // this would check against document.state.edit_permissions
                    Ok(())
                } else {
                    Ok(())
                }
            },
            _ => Ok(()),
        }
    }
    
    /// Create a document change from command execution
    pub fn create_document_change(
        change_type: ChangeType,
        page: Option<u32>,
        description: String,
        previous_state: ChangeState,
        new_state: ChangeState,
    ) -> DocumentChange {
        DocumentChange::new(change_type, page, description, previous_state, new_state)
    }
}

/// Re-export commonly used types
pub use base::Command;
pub use command_manager::CommandManager;
