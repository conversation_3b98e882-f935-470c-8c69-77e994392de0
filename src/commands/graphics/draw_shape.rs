/// Draw shape command implementation
/// 
/// This command handles drawing geometric shapes on PDF pages.

use super::*;
use crate::commands::{Command, CommandMetadata, CommandResult, CommandError, ExecutionContext, ValidationResult};
use crate::document::{DocumentChange, ChangeType, ChangeState, Color};
use crate::types::{Point, Rect};
use crate::impl_command_base;

use std::any::Any;

/// Shape type enumeration
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ShapeType {
    Rectangle,
    Circle,
    Line,
    Polygon,
    Ellipse,
}

/// Shape style configuration
#[derive(Debug, Clone)]
pub struct ShapeStyle {
    /// Fill color (None for no fill)
    pub fill_color: Option<Color>,
    /// Stroke color (None for no stroke)
    pub stroke_color: Option<Color>,
    /// Stroke width
    pub stroke_width: f32,
    /// Line dash pattern
    pub dash_pattern: Vec<f32>,
    /// Opacity (0.0 to 1.0)
    pub opacity: f32,
}

impl Default for ShapeStyle {
    fn default() -> Self {
        Self {
            fill_color: None,
            stroke_color: Some(Color::black()),
            stroke_width: 1.0,
            dash_pattern: Vec::new(),
            opacity: 1.0,
        }
    }
}

/// Command for drawing shapes
#[derive(Debug, Clone)]
pub struct DrawShapeCommand {
    base: crate::commands::base::BaseCommand,
    page: u32,
    shape_type: ShapeType,
    bounds: Rect,
    style: ShapeStyle,
    points: Vec<Point>, // For polygons and complex shapes
    drawn_shape_id: Option<String>,
    previous_state: Option<ChangeState>,
}

impl DrawShapeCommand {
    /// Create a new draw shape command
    pub fn new(
        page: u32,
        shape_type: ShapeType,
        bounds: Rect,
    ) -> Self {
        let metadata = CommandMetadata::new(
            "DrawShape".to_string(),
            format!("Draw {:?} on page {} at ({}, {})", 
                    shape_type, page, bounds.x, bounds.y)
        );
        
        Self {
            base: crate::commands::base::BaseCommand::new(metadata),
            page,
            shape_type,
            bounds,
            style: ShapeStyle::default(),
            points: Vec::new(),
            drawn_shape_id: None,
            previous_state: None,
        }
    }
    
    /// Create a rectangle command
    pub fn rectangle(page: u32, bounds: Rect) -> Self {
        Self::new(page, ShapeType::Rectangle, bounds)
    }
    
    /// Create a circle command
    pub fn circle(page: u32, center: Point, radius: f32) -> Self {
        let bounds = Rect::new(
            center.x - radius,
            center.y - radius,
            radius * 2.0,
            radius * 2.0,
        );
        Self::new(page, ShapeType::Circle, bounds)
    }
    
    /// Create a line command
    pub fn line(page: u32, start: Point, end: Point) -> Self {
        let bounds = Rect::new(
            start.x.min(end.x),
            start.y.min(end.y),
            (end.x - start.x).abs(),
            (end.y - start.y).abs(),
        );
        let mut cmd = Self::new(page, ShapeType::Line, bounds);
        cmd.points = vec![start, end];
        cmd
    }
    
    /// Create a polygon command
    pub fn polygon(page: u32, points: Vec<Point>) -> Self {
        let bounds = calculate_bounds_from_points(&points);
        let mut cmd = Self::new(page, ShapeType::Polygon, bounds);
        cmd.points = points;
        cmd
    }
    
    /// Set shape style
    pub fn with_style(mut self, style: ShapeStyle) -> Self {
        self.style = style;
        self
    }
    
    /// Set fill color
    pub fn with_fill(mut self, color: Color) -> Self {
        self.style.fill_color = Some(color);
        self
    }
    
    /// Set stroke color and width
    pub fn with_stroke(mut self, color: Color, width: f32) -> Self {
        self.style.stroke_color = Some(color);
        self.style.stroke_width = width;
        self
    }
    
    /// Set opacity
    pub fn with_opacity(mut self, opacity: f32) -> Self {
        self.style.opacity = opacity.clamp(0.0, 1.0);
        self
    }
    
    /// Get the page number
    pub fn page(&self) -> u32 {
        self.page
    }
    
    /// Get the shape type
    pub fn shape_type(&self) -> ShapeType {
        self.shape_type
    }
    
    /// Get the shape bounds
    pub fn bounds(&self) -> Rect {
        self.bounds
    }
    
    /// Get the drawn shape ID (available after execution)
    pub fn drawn_shape_id(&self) -> Option<&str> {
        self.drawn_shape_id.as_deref()
    }
}

impl_command_base!(DrawShapeCommand);

impl Command for DrawShapeCommand {
    fn validate(&self, context: &ExecutionContext) -> ValidationResult {
        let mut result = ValidationResult::valid();
        
        // Validate page exists
        if self.page == 0 || self.page > context.document.page_count() {
            result.add_error(format!("Invalid page number: {}", self.page));
        }
        
        // Validate bounds
        if self.bounds.width <= 0.0 || self.bounds.height <= 0.0 {
            result.add_error("Shape bounds must have positive width and height".to_string());
        }
        
        // Validate polygon points
        if self.shape_type == ShapeType::Polygon && self.points.len() < 3 {
            result.add_error("Polygon must have at least 3 points".to_string());
        }
        
        // Validate line points
        if self.shape_type == ShapeType::Line && self.points.len() != 2 {
            result.add_error("Line must have exactly 2 points".to_string());
        }
        
        // Validate style
        if self.style.stroke_width < 0.0 {
            result.add_error("Stroke width cannot be negative".to_string());
        }
        
        if self.style.opacity < 0.0 || self.style.opacity > 1.0 {
            result.add_error("Opacity must be between 0.0 and 1.0".to_string());
        }
        
        // Check if shape has visible properties
        if self.style.fill_color.is_none() && self.style.stroke_color.is_none() {
            result.add_warning("Shape has no fill or stroke color and may not be visible".to_string());
        }
        
        // Check permissions
        if let Err(e) = crate::commands::utils::check_permission(&context.document, "edit_graphics") {
            result.add_error(e.to_string());
        }
        
        result
    }
    
    fn execute(&mut self, context: &mut ExecutionContext) -> CommandResult<()> {
        // Generate unique shape ID
        let shape_id = format!("shape_{}", generate_unique_id());
        
        // Store previous state for undo
        self.previous_state = Some(create_shape_change_state(
            self.page,
            &shape_id,
            self.shape_type,
            self.bounds,
            false, // Shape doesn't exist yet
        ));
        
        // Draw shape on page
        let page = context.document.get_page_mut(self.page)
            .map_err(|e| CommandError::ExecutionFailed(e.to_string()))?;
        
        draw_shape_on_page(page, &shape_id, self.shape_type, self.bounds, &self.style, &self.points)?;
        
        self.drawn_shape_id = Some(shape_id.clone());
        
        // Create and record the document change
        let new_state = create_shape_change_state(
            self.page,
            &shape_id,
            self.shape_type,
            self.bounds,
            true, // Shape now exists
        );
        
        let change = DocumentChange::new(
            ChangeType::ShapeInsert,
            Some(self.page),
            format!("Draw {:?} at ({}, {})", self.shape_type, self.bounds.x, self.bounds.y),
            self.previous_state.clone().unwrap(),
            new_state,
        );
        
        // Add user information if available
        let change = if let Some(ref user) = context.user_info {
            change.with_author(user.name.clone())
        } else {
            change
        };
        
        self.set_change(change.clone());
        context.record_change(change);
        
        self.base.set_executed(true);
        Ok(())
    }
    
    fn undo(&mut self, context: &mut ExecutionContext) -> CommandResult<()> {
        if !self.base.is_executed() {
            return Err(CommandError::UndoRedoFailed(
                "Command has not been executed".to_string()
            ));
        }
        
        let shape_id = self.drawn_shape_id.as_ref()
            .ok_or_else(|| CommandError::UndoRedoFailed(
                "No drawn shape ID available for undo".to_string()
            ))?;
        
        // Remove shape from page
        let page = context.document.get_page_mut(self.page)
            .map_err(|e| CommandError::UndoRedoFailed(e.to_string()))?;
        
        remove_shape_from_page(page, shape_id)?;
        
        self.base.set_executed(false);
        Ok(())
    }
    
    fn redo(&mut self, context: &mut ExecutionContext) -> CommandResult<()> {
        if self.base.is_executed() {
            return Err(CommandError::UndoRedoFailed(
                "Command is already executed".to_string()
            ));
        }
        
        // Re-execute the command
        self.execute(context)
    }
}

// Helper functions

fn calculate_bounds_from_points(points: &[Point]) -> Rect {
    if points.is_empty() {
        return Rect::new(0.0, 0.0, 0.0, 0.0);
    }
    
    let mut min_x = points[0].x;
    let mut max_x = points[0].x;
    let mut min_y = points[0].y;
    let mut max_y = points[0].y;
    
    for point in points.iter().skip(1) {
        min_x = min_x.min(point.x);
        max_x = max_x.max(point.x);
        min_y = min_y.min(point.y);
        max_y = max_y.max(point.y);
    }
    
    Rect::new(min_x, min_y, max_x - min_x, max_y - min_y)
}

fn generate_unique_id() -> u64 {
    use std::collections::hash_map::DefaultHasher;
    use std::hash::{Hash, Hasher};
    use std::time::{SystemTime, UNIX_EPOCH};
    
    let mut hasher = DefaultHasher::new();
    SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_nanos().hash(&mut hasher);
    hasher.finish()
}

fn create_shape_change_state(
    page: u32,
    shape_id: &str,
    shape_type: ShapeType,
    bounds: Rect,
    exists: bool,
) -> ChangeState {
    ChangeState::ShapeState {
        page,
        shape_id: shape_id.to_string(),
        shape_type: format!("{:?}", shape_type),
        bounds,
        exists,
    }
}

fn draw_shape_on_page(
    page: &mut crate::document::EditablePage,
    shape_id: &str,
    shape_type: ShapeType,
    bounds: Rect,
    style: &ShapeStyle,
    points: &[Point],
) -> CommandResult<()> {
    // In a real implementation, this would:
    // 1. Create a shape object with the specified properties
    // 2. Add it to the appropriate page layer
    // 3. Update the page layout and rendering
    
    Ok(())
}

fn remove_shape_from_page(
    page: &mut crate::document::EditablePage,
    shape_id: &str,
) -> CommandResult<()> {
    // In a real implementation, this would:
    // 1. Find the shape object by ID
    // 2. Remove it from the page layer
    // 3. Update the page layout
    
    Ok(())
}
