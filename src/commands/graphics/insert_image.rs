/// Insert image command implementation
/// 
/// This command handles image insertion into PDF pages.

use super::*;
use crate::commands::{Command, CommandMetadata, CommandResult, CommandError, ExecutionContext, ValidationResult};
use crate::document::{DocumentChange, ChangeType, ChangeState};
use crate::types::{Point, Rect};
use crate::impl_command_base;

use std::any::Any;

/// Command for inserting an image
#[derive(Debug, Clone)]
pub struct InsertImageCommand {
    base: crate::commands::base::BaseCommand,
    page: u32,
    position: Point,
    image_data: Vec<u8>,
    image_format: crate::resources::ImageFormat,
    size: Option<Rect>,
    inserted_image_id: Option<String>,
    previous_state: Option<ChangeState>,
}

impl InsertImageCommand {
    /// Create a new insert image command
    pub fn new(
        page: u32,
        position: Point,
        image_data: Vec<u8>,
        image_format: crate::resources::ImageFormat,
    ) -> Self {
        let metadata = CommandMetadata::new(
            "InsertImage".to_string(),
            format!("Insert {} image at page {} position ({}, {})", 
                    image_format.extension(), page, position.x, position.y)
        );
        
        Self {
            base: crate::commands::base::BaseCommand::new(metadata),
            page,
            position,
            image_data,
            image_format,
            size: None,
            inserted_image_id: None,
            previous_state: None,
        }
    }
    
    /// Set custom image size
    pub fn with_size(mut self, size: Rect) -> Self {
        self.size = Some(size);
        self
    }
    
    /// Get the page number
    pub fn page(&self) -> u32 {
        self.page
    }
    
    /// Get the insertion position
    pub fn position(&self) -> Point {
        self.position
    }
    
    /// Get the image format
    pub fn image_format(&self) -> crate::resources::ImageFormat {
        self.image_format
    }
    
    /// Get the inserted image ID (available after execution)
    pub fn inserted_image_id(&self) -> Option<&str> {
        self.inserted_image_id.as_deref()
    }
}

impl_command_base!(InsertImageCommand);

impl Command for InsertImageCommand {
    fn validate(&self, context: &ExecutionContext) -> ValidationResult {
        let mut result = ValidationResult::valid();
        
        // Validate page exists
        if self.page == 0 || self.page > context.document.page_count() {
            result.add_error(format!("Invalid page number: {}", self.page));
        }
        
        // Validate image data
        if self.image_data.is_empty() {
            result.add_error("Image data is empty".to_string());
        }
        
        // Validate image format
        if !is_supported_image_format(self.image_format) {
            result.add_error(format!("Unsupported image format: {:?}", self.image_format));
        }
        
        // Validate position
        if let Ok(page) = context.document.get_page(self.page) {
            let page_bounds = get_page_bounds(page);
            if !page_bounds.contains_point(self.position) {
                result.add_warning("Image position is outside page bounds".to_string());
            }
        }
        
        // Check permissions
        if let Err(e) = crate::commands::utils::check_permission(&context.document, "edit_graphics") {
            result.add_error(e.to_string());
        }
        
        result
    }
    
    fn execute(&mut self, context: &mut ExecutionContext) -> CommandResult<()> {
        // Load image into resource manager
        let image_id = context.document.resources()
            .lock()
            .unwrap()
            .image_manager_mut()
            .load_image(
                format!("image_{}", uuid::Uuid::new_v4()),
                self.image_data.clone(),
            )
            .map_err(|e| CommandError::ExecutionFailed(e.to_string()))?;
        
        // Get image dimensions
        let image_resource = context.document.resources()
            .lock()
            .unwrap()
            .image_manager()
            .get_image_by_id(&image_id)
            .ok_or_else(|| CommandError::ExecutionFailed("Failed to load image".to_string()))?;
        
        // Calculate image size if not provided
        let image_size = self.size.unwrap_or_else(|| {
            Rect::new(
                self.position.x,
                self.position.y,
                image_resource.width as f32,
                image_resource.height as f32,
            )
        });
        
        // Store previous state for undo
        self.previous_state = Some(create_image_change_state(
            self.page,
            &image_id,
            image_size,
            false, // Image doesn't exist yet
        ));
        
        // Insert image into page
        let page = context.document.get_page_mut(self.page)
            .map_err(|e| CommandError::ExecutionFailed(e.to_string()))?;
        
        insert_image_into_page(page, &image_id, image_size)?;
        
        self.inserted_image_id = Some(image_id.clone());
        
        // Create and record the document change
        let new_state = create_image_change_state(
            self.page,
            &image_id,
            image_size,
            true, // Image now exists
        );
        
        let change = DocumentChange::new(
            ChangeType::ImageInsert,
            Some(self.page),
            format!("Insert {} image at ({}, {})", 
                    self.image_format.extension(), self.position.x, self.position.y),
            self.previous_state.clone().unwrap(),
            new_state,
        );
        
        // Add user information if available
        let change = if let Some(ref user) = context.user_info {
            change.with_author(user.name.clone())
        } else {
            change
        };
        
        self.set_change(change.clone());
        context.record_change(change);
        
        self.base.set_executed(true);
        Ok(())
    }
    
    fn undo(&mut self, context: &mut ExecutionContext) -> CommandResult<()> {
        if !self.base.is_executed() {
            return Err(CommandError::UndoRedoFailed(
                "Command has not been executed".to_string()
            ));
        }
        
        let image_id = self.inserted_image_id.as_ref()
            .ok_or_else(|| CommandError::UndoRedoFailed(
                "No inserted image ID available for undo".to_string()
            ))?;
        
        // Remove image from page
        let page = context.document.get_page_mut(self.page)
            .map_err(|e| CommandError::UndoRedoFailed(e.to_string()))?;
        
        remove_image_from_page(page, image_id)?;
        
        // Remove image from resource manager
        context.document.resources()
            .lock()
            .unwrap()
            .image_manager_mut()
            .remove_image(image_id)
            .map_err(|e| CommandError::UndoRedoFailed(e.to_string()))?;
        
        self.base.set_executed(false);
        Ok(())
    }
    
    fn redo(&mut self, context: &mut ExecutionContext) -> CommandResult<()> {
        if self.base.is_executed() {
            return Err(CommandError::UndoRedoFailed(
                "Command is already executed".to_string()
            ));
        }
        
        // Re-execute the command
        self.execute(context)
    }
}

// Helper functions

fn is_supported_image_format(format: crate::resources::ImageFormat) -> bool {
    matches!(format, 
        crate::resources::ImageFormat::PNG |
        crate::resources::ImageFormat::JPEG |
        crate::resources::ImageFormat::GIF |
        crate::resources::ImageFormat::BMP |
        crate::resources::ImageFormat::WEBP
    )
}

fn get_page_bounds(page: &crate::document::EditablePage) -> Rect {
    // Get page bounds from page settings
    Rect::new(0.0, 0.0, 612.0, 792.0) // Default letter size
}

fn create_image_change_state(
    page: u32,
    image_id: &str,
    bounds: Rect,
    exists: bool,
) -> ChangeState {
    ChangeState::ImageState {
        page,
        image_id: image_id.to_string(),
        bounds,
        exists,
    }
}

fn insert_image_into_page(
    page: &mut crate::document::EditablePage,
    image_id: &str,
    bounds: Rect,
) -> CommandResult<()> {
    // In a real implementation, this would:
    // 1. Create an image object
    // 2. Add it to the appropriate page layer
    // 3. Update the page layout
    
    Ok(())
}

fn remove_image_from_page(
    page: &mut crate::document::EditablePage,
    image_id: &str,
) -> CommandResult<()> {
    // In a real implementation, this would:
    // 1. Find the image object by ID
    // 2. Remove it from the page layer
    // 3. Update the page layout
    
    Ok(())
}

// Add uuid dependency for generating unique IDs
mod uuid {
    pub struct Uuid;
    
    impl Uuid {
        pub fn new_v4() -> Self {
            Self
        }
    }
    
    impl std::fmt::Display for Uuid {
        fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
            write!(f, "uuid-{}", std::ptr::addr_of!(*self) as usize)
        }
    }
}
