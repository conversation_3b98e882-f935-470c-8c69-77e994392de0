/// Delete text command implementation
/// 
/// This command handles text deletion within a specified range.

use super::*;
use crate::commands::{Command, CommandMetadata, CommandResult, CommandError, ExecutionContext, ValidationResult};
use crate::document::{DocumentChange, ChangeType, ChangeState};
use crate::impl_command_base;

use std::any::Any;

/// Command for deleting text in a specified range
#[derive(Debug, Clone)]
pub struct DeleteTextCommand {
    base: crate::commands::base::BaseCommand,
    page: u32,
    start: usize,
    end: usize,
    deleted_text: Option<String>,
    previous_state: Option<ChangeState>,
}

impl DeleteTextCommand {
    /// Create a new delete text command
    pub fn new(page: u32, start: usize, end: usize) -> Self {
        let metadata = CommandMetadata::new(
            "DeleteText".to_string(),
            format!("Delete text from position {} to {} on page {}", start, end, page)
        );
        
        Self {
            base: crate::commands::base::BaseCommand::new(metadata),
            page,
            start,
            end,
            deleted_text: None,
            previous_state: None,
        }
    }
    
    /// Get the page number
    pub fn page(&self) -> u32 {
        self.page
    }
    
    /// Get the start position
    pub fn start(&self) -> usize {
        self.start
    }
    
    /// Get the end position
    pub fn end(&self) -> usize {
        self.end
    }
    
    /// Get the deleted text (available after execution)
    pub fn deleted_text(&self) -> Option<&str> {
        self.deleted_text.as_deref()
    }
    
    /// Get the length of text to be deleted
    pub fn length(&self) -> usize {
        self.end.saturating_sub(self.start)
    }
}

impl_command_base!(DeleteTextCommand);

impl Command for DeleteTextCommand {
    fn validate(&self, context: &ExecutionContext) -> ValidationResult {
        let mut result = ValidationResult::valid();
        
        // Validate page number
        if let Err(e) = crate::commands::utils::validate_page_number(self.page, &context.document) {
            result.add_error(e.to_string());
        }
        
        // Validate range
        if self.start > self.end {
            result.add_error("Start position cannot be greater than end position".to_string());
        }
        
        if self.start == self.end {
            result.add_warning("Deleting empty range has no effect".to_string());
        }
        
        // Validate positions
        if let Err(e) = super::utils::validate_text_position_in_page(&context.document, self.page, self.start) {
            result.add_error(e.to_string());
        }
        
        if let Err(e) = super::utils::validate_text_position_in_page(&context.document, self.page, self.end) {
            result.add_error(e.to_string());
        }
        
        // Check permissions
        if let Err(e) = crate::commands::utils::check_permission(&context.document, "edit_text") {
            result.add_error(e.to_string());
        }
        
        result
    }
    
    fn execute(&mut self, context: &mut ExecutionContext) -> CommandResult<()> {
        // Store the previous state for undo
        let previous_content = super::utils::get_page_text_content(&context.document, self.page)?;
        let previous_format = super::utils::get_text_formatting_at_position(
            &context.document, 
            self.page, 
            self.start
        )?;
        
        self.previous_state = Some(super::utils::create_text_change_state(
            previous_content,
            self.start,
            previous_format,
        ));
        
        // Execute the text deletion
        let deleted_text = super::utils::delete_text_range(
            &mut context.document,
            self.page,
            self.start,
            self.end,
        )?;
        
        self.deleted_text = Some(deleted_text.clone());
        
        // Create and record the document change
        let new_content = super::utils::get_page_text_content(&context.document, self.page)?;
        let new_state = super::utils::create_text_change_state(
            new_content,
            self.start,
            None,
        );
        
        let change = DocumentChange::new(
            ChangeType::TextDelete,
            Some(self.page),
            format!("Delete '{}' from position {} to {}", deleted_text, self.start, self.end),
            self.previous_state.clone().unwrap(),
            new_state,
        );
        
        // Add user information if available
        let change = if let Some(ref user) = context.user_info {
            change.with_author(user.name.clone())
        } else {
            change
        };
        
        self.set_change(change.clone());
        context.record_change(change);
        
        self.base.set_executed(true);
        Ok(())
    }
    
    fn undo(&mut self, context: &mut ExecutionContext) -> CommandResult<()> {
        if !self.base.is_executed() {
            return Err(CommandError::UndoRedoFailed(
                "Command has not been executed".to_string()
            ));
        }
        
        let deleted_text = self.deleted_text.as_ref()
            .ok_or_else(|| CommandError::UndoRedoFailed(
                "No deleted text available for undo".to_string()
            ))?;
        
        // Re-insert the deleted text
        super::utils::insert_text_at_position(
            &mut context.document,
            self.page,
            self.start,
            deleted_text,
            None, // TODO: Restore original formatting
        )?;
        
        self.base.set_executed(false);
        Ok(())
    }
    
    fn redo(&mut self, context: &mut ExecutionContext) -> CommandResult<()> {
        if self.base.is_executed() {
            return Err(CommandError::UndoRedoFailed(
                "Command is already executed".to_string()
            ));
        }
        
        // Re-execute the command
        self.execute(context)
    }
}
