/// Replace text command implementation
/// 
/// This command handles text replacement within a specified range.

use super::*;
use crate::commands::{Command, CommandMetadata, CommandResult, CommandError, ExecutionContext, ValidationResult};
use crate::document::{TextFormat, DocumentChange, ChangeType, ChangeState};
use crate::impl_command_base;

use std::any::Any;

/// Command for replacing text in a specified range
#[derive(Debug, Clone)]
pub struct ReplaceTextCommand {
    base: crate::commands::base::BaseCommand,
    page: u32,
    start: usize,
    end: usize,
    new_text: String,
    format: Option<TextFormat>,
    old_text: Option<String>,
    previous_state: Option<ChangeState>,
}

impl ReplaceTextCommand {
    /// Create a new replace text command
    pub fn new(
        page: u32,
        start: usize,
        end: usize,
        new_text: String,
        format: Option<TextFormat>,
    ) -> Self {
        let metadata = CommandMetadata::new(
            "ReplaceText".to_string(),
            format!("Replace text from position {} to {} with '{}' on page {}", 
                    start, end, 
                    if new_text.len() > 20 { 
                        format!("{}...", &new_text[..20]) 
                    } else { 
                        new_text.clone() 
                    }, 
                    page)
        );
        
        Self {
            base: crate::commands::base::BaseCommand::new(metadata),
            page,
            start,
            end,
            new_text,
            format,
            old_text: None,
            previous_state: None,
        }
    }
    
    /// Get the page number
    pub fn page(&self) -> u32 {
        self.page
    }
    
    /// Get the start position
    pub fn start(&self) -> usize {
        self.start
    }
    
    /// Get the end position
    pub fn end(&self) -> usize {
        self.end
    }
    
    /// Get the new text
    pub fn new_text(&self) -> &str {
        &self.new_text
    }
    
    /// Get the text format
    pub fn format(&self) -> Option<&TextFormat> {
        self.format.as_ref()
    }
    
    /// Get the old text (available after execution)
    pub fn old_text(&self) -> Option<&str> {
        self.old_text.as_deref()
    }
}

impl_command_base!(ReplaceTextCommand);

impl Command for ReplaceTextCommand {
    fn validate(&self, context: &ExecutionContext) -> ValidationResult {
        let mut result = ValidationResult::valid();
        
        // Validate page number
        if let Err(e) = crate::commands::utils::validate_page_number(self.page, &context.document) {
            result.add_error(e.to_string());
        }
        
        // Validate range
        if self.start > self.end {
            result.add_error("Start position cannot be greater than end position".to_string());
        }
        
        // Validate positions
        if let Err(e) = super::utils::validate_text_position_in_page(&context.document, self.page, self.start) {
            result.add_error(e.to_string());
        }
        
        if let Err(e) = super::utils::validate_text_position_in_page(&context.document, self.page, self.end) {
            result.add_error(e.to_string());
        }
        
        // Validate new text
        if self.new_text.len() > 10000 {
            result.add_warning("Replacing with very long text may impact performance".to_string());
        }
        
        // Check permissions
        if let Err(e) = crate::commands::utils::check_permission(&context.document, "edit_text") {
            result.add_error(e.to_string());
        }
        
        result
    }
    
    fn execute(&mut self, context: &mut ExecutionContext) -> CommandResult<()> {
        // Store the previous state for undo
        let previous_content = super::utils::get_page_text_content(&context.document, self.page)?;
        let previous_format = super::utils::get_text_formatting_at_position(
            &context.document, 
            self.page, 
            self.start
        )?;
        
        self.previous_state = Some(super::utils::create_text_change_state(
            previous_content,
            self.start,
            previous_format,
        ));
        
        // Execute the text replacement
        let old_text = super::utils::replace_text_range(
            &mut context.document,
            self.page,
            self.start,
            self.end,
            &self.new_text,
            self.format.clone(),
        )?;
        
        self.old_text = Some(old_text.clone());
        
        // Create and record the document change
        let new_content = super::utils::get_page_text_content(&context.document, self.page)?;
        let new_state = super::utils::create_text_change_state(
            new_content,
            self.start + self.new_text.len(),
            self.format.clone(),
        );
        
        let change = DocumentChange::new(
            ChangeType::TextInsert, // Replace is essentially delete + insert
            Some(self.page),
            format!("Replace '{}' with '{}' from position {} to {}", 
                    old_text, self.new_text, self.start, self.end),
            self.previous_state.clone().unwrap(),
            new_state,
        );
        
        // Add user information if available
        let change = if let Some(ref user) = context.user_info {
            change.with_author(user.name.clone())
        } else {
            change
        };
        
        self.set_change(change.clone());
        context.record_change(change);
        
        self.base.set_executed(true);
        Ok(())
    }
    
    fn undo(&mut self, context: &mut ExecutionContext) -> CommandResult<()> {
        if !self.base.is_executed() {
            return Err(CommandError::UndoRedoFailed(
                "Command has not been executed".to_string()
            ));
        }
        
        let old_text = self.old_text.as_ref()
            .ok_or_else(|| CommandError::UndoRedoFailed(
                "No old text available for undo".to_string()
            ))?;
        
        // Replace the new text with the old text
        super::utils::replace_text_range(
            &mut context.document,
            self.page,
            self.start,
            self.start + self.new_text.len(),
            old_text,
            None, // TODO: Restore original formatting
        )?;
        
        self.base.set_executed(false);
        Ok(())
    }
    
    fn redo(&mut self, context: &mut ExecutionContext) -> CommandResult<()> {
        if self.base.is_executed() {
            return Err(CommandError::UndoRedoFailed(
                "Command is already executed".to_string()
            ));
        }
        
        // Re-execute the command
        self.execute(context)
    }
}
