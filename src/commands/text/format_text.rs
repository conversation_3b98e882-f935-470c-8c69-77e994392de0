/// Format text command implementation
/// 
/// This command handles text formatting changes within a specified range.

use super::*;
use crate::commands::{Command, CommandMetadata, CommandResult, CommandError, ExecutionContext, ValidationResult};
use crate::document::{TextFormat, DocumentChange, ChangeType, ChangeState};
use crate::impl_command_base;

use std::any::Any;

/// Command for applying text formatting to a range
#[derive(Debug, Clone)]
pub struct FormatTextCommand {
    base: crate::commands::base::BaseCommand,
    page: u32,
    start: usize,
    end: usize,
    format: TextFormat,
    previous_format: Option<TextFormat>,
    previous_state: Option<ChangeState>,
}

impl FormatTextCommand {
    /// Create a new format text command
    pub fn new(page: u32, start: usize, end: usize, format: TextFormat) -> Self {
        let metadata = CommandMetadata::new(
            "FormatText".to_string(),
            format!("Apply formatting to text from position {} to {} on page {}", start, end, page)
        );
        
        Self {
            base: crate::commands::base::BaseCommand::new(metadata),
            page,
            start,
            end,
            format,
            previous_format: None,
            previous_state: None,
        }
    }
    
    /// Get the page number
    pub fn page(&self) -> u32 {
        self.page
    }
    
    /// Get the start position
    pub fn start(&self) -> usize {
        self.start
    }
    
    /// Get the end position
    pub fn end(&self) -> usize {
        self.end
    }
    
    /// Get the text format
    pub fn format(&self) -> &TextFormat {
        &self.format
    }
    
    /// Get the previous format (available after execution)
    pub fn previous_format(&self) -> Option<&TextFormat> {
        self.previous_format.as_ref()
    }
}

impl_command_base!(FormatTextCommand);

impl Command for FormatTextCommand {
    fn validate(&self, context: &ExecutionContext) -> ValidationResult {
        let mut result = ValidationResult::valid();
        
        // Validate page number
        if let Err(e) = crate::commands::utils::validate_page_number(self.page, &context.document) {
            result.add_error(e.to_string());
        }
        
        // Validate range
        if self.start > self.end {
            result.add_error("Start position cannot be greater than end position".to_string());
        }
        
        if self.start == self.end {
            result.add_warning("Formatting empty range has no effect".to_string());
        }
        
        // Validate positions
        if let Err(e) = super::utils::validate_text_position_in_page(&context.document, self.page, self.start) {
            result.add_error(e.to_string());
        }
        
        if let Err(e) = super::utils::validate_text_position_in_page(&context.document, self.page, self.end) {
            result.add_error(e.to_string());
        }
        
        // Validate format
        if self.format.font_size <= 0.0 {
            result.add_error("Font size must be positive".to_string());
        }
        
        if self.format.font_size > 200.0 {
            result.add_warning("Very large font size may cause layout issues".to_string());
        }
        
        // Check permissions
        if let Err(e) = crate::commands::utils::check_permission(&context.document, "edit_text") {
            result.add_error(e.to_string());
        }
        
        result
    }
    
    fn execute(&mut self, context: &mut ExecutionContext) -> CommandResult<()> {
        // Store the previous state for undo
        let previous_content = super::utils::get_page_text_content(&context.document, self.page)?;
        self.previous_format = super::utils::get_text_formatting_at_position(
            &context.document, 
            self.page, 
            self.start
        )?;
        
        self.previous_state = Some(super::utils::create_text_change_state(
            previous_content,
            self.start,
            self.previous_format.clone(),
        ));
        
        // Apply the text formatting
        super::utils::apply_text_formatting(
            &mut context.document,
            self.page,
            self.start,
            self.end,
            self.format.clone(),
        )?;
        
        // Create and record the document change
        let new_content = super::utils::get_page_text_content(&context.document, self.page)?;
        let new_state = super::utils::create_text_change_state(
            new_content,
            self.start,
            Some(self.format.clone()),
        );
        
        let change = DocumentChange::new(
            ChangeType::TextFormat,
            Some(self.page),
            format!("Apply formatting to text from position {} to {}", self.start, self.end),
            self.previous_state.clone().unwrap(),
            new_state,
        );
        
        // Add user information if available
        let change = if let Some(ref user) = context.user_info {
            change.with_author(user.name.clone())
        } else {
            change
        };
        
        self.set_change(change.clone());
        context.record_change(change);
        
        self.base.set_executed(true);
        Ok(())
    }
    
    fn undo(&mut self, context: &mut ExecutionContext) -> CommandResult<()> {
        if !self.base.is_executed() {
            return Err(CommandError::UndoRedoFailed(
                "Command has not been executed".to_string()
            ));
        }
        
        // Restore the previous formatting
        if let Some(ref previous_format) = self.previous_format {
            super::utils::apply_text_formatting(
                &mut context.document,
                self.page,
                self.start,
                self.end,
                previous_format.clone(),
            )?;
        }
        
        self.base.set_executed(false);
        Ok(())
    }
    
    fn redo(&mut self, context: &mut ExecutionContext) -> CommandResult<()> {
        if self.base.is_executed() {
            return Err(CommandError::UndoRedoFailed(
                "Command is already executed".to_string()
            ));
        }
        
        // Re-execute the command
        self.execute(context)
    }
}
