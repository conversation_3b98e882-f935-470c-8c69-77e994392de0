/// Text editing commands
/// 
/// This module provides commands for text manipulation operations
/// including insertion, deletion, formatting, and movement.

pub mod insert_text;
pub mod delete_text;
pub mod format_text;
pub mod replace_text;

pub use insert_text::InsertTextCommand;
pub use delete_text::DeleteTextCommand;
pub use format_text::FormatTextCommand;
pub use replace_text::ReplaceTextCommand;

use super::*;
use crate::document::{EditableDocument, TextFormat, DocumentChange, ChangeType};
use crate::document::change_tracker::ChangeState;

/// Text command utilities
pub mod utils {
    use super::*;
    
    /// Find text object at position on a page
    pub fn find_text_object_at_position(
        document: &EditableDocument,
        page: u32,
        position: usize,
    ) -> CommandResult<Option<String>> {
        let page_obj = document.get_page(page)
            .map_err(|e| CommandError::InvalidParameters(e.to_string()))?;
        
        // In a real implementation, this would search through text objects
        // For now, we'll return a placeholder
        Ok(Some("text_object_id".to_string()))
    }
    
    /// Get text content from a page
    pub fn get_page_text_content(
        document: &EditableDocument,
        page: u32,
    ) -> CommandResult<String> {
        let page_obj = document.get_page(page)
            .map_err(|e| CommandError::InvalidParameters(e.to_string()))?;

        let mut content = String::new();

        // Extract text from all layers, ordered by position
        let mut text_objects = Vec::new();

        for layer in page_obj.layers() {
            for object in &layer.objects {
                if let crate::document::PageObject::Text(text_obj) = object {
                    text_objects.push(text_obj);
                }
            }
        }

        // Sort text objects by position (top to bottom, left to right)
        text_objects.sort_by(|a, b| {
            let y_cmp = a.bounds.y.partial_cmp(&b.bounds.y).unwrap_or(std::cmp::Ordering::Equal);
            if y_cmp == std::cmp::Ordering::Equal {
                a.bounds.x.partial_cmp(&b.bounds.x).unwrap_or(std::cmp::Ordering::Equal)
            } else {
                y_cmp
            }
        });

        // Concatenate text content
        for (i, text_obj) in text_objects.iter().enumerate() {
            content.push_str(&text_obj.text);
            // Add space between text objects on same line, newline for different lines
            if i < text_objects.len() - 1 {
                let next_obj = text_objects[i + 1];
                if (next_obj.bounds.y - text_obj.bounds.y).abs() > text_obj.bounds.height / 2.0 {
                    content.push('\n'); // Different line
                } else {
                    content.push(' '); // Same line
                }
            }
        }

        Ok(content)
    }
    
    /// Validate text position within page content
    pub fn validate_text_position_in_page(
        document: &EditableDocument,
        page: u32,
        position: usize,
    ) -> CommandResult<()> {
        let text_content = get_page_text_content(document, page)?;
        
        if position > text_content.len() {
            return Err(CommandError::InvalidParameters(
                format!("Position {} exceeds text length {}", position, text_content.len())
            ));
        }
        
        Ok(())
    }
    
    /// Create a text change state
    pub fn create_text_change_state(
        content: String,
        position: usize,
        format: Option<TextFormat>,
    ) -> ChangeState {
        ChangeState::Text {
            content,
            position,
            format,
        }
    }
    
    /// Apply text formatting to a range
    pub fn apply_text_formatting(
        document: &mut EditableDocument,
        page: u32,
        start: usize,
        end: usize,
        format: TextFormat,
    ) -> CommandResult<()> {
        let _page_obj = document.get_page_mut(page)
            .map_err(|e| CommandError::ExecutionFailed(e.to_string()))?;
        
        // In a real implementation, this would apply formatting to text objects
        // For now, we'll just validate the parameters
        if start > end {
            return Err(CommandError::InvalidParameters(
                "Start position cannot be greater than end position".to_string()
            ));
        }
        
        Ok(())
    }
    
    /// Insert text at a specific position
    pub fn insert_text_at_position(
        document: &mut EditableDocument,
        page: u32,
        position: usize,
        text: &str,
        format: Option<TextFormat>,
    ) -> CommandResult<()> {
        let page_obj = document.get_page_mut(page)
            .map_err(|e| CommandError::ExecutionFailed(e.to_string()))?;

        // Validate the position
        validate_text_position_in_page(document, page, position)?;

        // Find or create text object at position
        let text_object_id = find_or_create_text_object_at_position(page_obj, position)?;

        // Insert the text with formatting
        insert_text_in_object(page_obj, &text_object_id, position, text, format)?;

        // Update page layout
        update_page_layout(page_obj)?;

        Ok(())
    }
    
    /// Delete text in a range
    pub fn delete_text_range(
        document: &mut EditableDocument,
        page: u32,
        start: usize,
        end: usize,
    ) -> CommandResult<String> {
        let _page_obj = document.get_page_mut(page)
            .map_err(|e| CommandError::ExecutionFailed(e.to_string()))?;
        
        // Validate range
        if start > end {
            return Err(CommandError::InvalidParameters(
                "Start position cannot be greater than end position".to_string()
            ));
        }
        
        validate_text_position_in_page(document, page, start)?;
        validate_text_position_in_page(document, page, end)?;
        
        // In a real implementation, this would:
        // 1. Find all text objects in the range
        // 2. Extract the text content
        // 3. Remove the text from the objects
        // 4. Merge text objects if necessary
        // 5. Update the page layout
        
        // Extract and delete the text
        let deleted_text = extract_text_range(document, page, start, end)?;
        delete_text_range_from_page(document, page, start, end)?;

        Ok(deleted_text)
    }
    
    /// Replace text in a range
    pub fn replace_text_range(
        document: &mut EditableDocument,
        page: u32,
        start: usize,
        end: usize,
        new_text: &str,
        format: Option<TextFormat>,
    ) -> CommandResult<String> {
        // Delete the old text and insert the new text
        let old_text = delete_text_range(document, page, start, end)?;
        insert_text_at_position(document, page, start, new_text, format)?;
        
        Ok(old_text)
    }
    

    
    /// Calculate text bounds for layout
    pub fn calculate_text_bounds(
        text: &str,
        format: &TextFormat,
        max_width: Option<f32>,
    ) -> CommandResult<crate::types::Rect> {
        // In a real implementation, this would use font metrics to calculate
        // the actual text bounds
        let estimated_width = text.len() as f32 * format.font_size * 0.6; // Rough estimate
        let height = format.font_size * 1.2; // Line height
        
        let width = if let Some(max_w) = max_width {
            estimated_width.min(max_w)
        } else {
            estimated_width
        };
        
        Ok(crate::types::Rect::new(0.0, 0.0, width, height))
    }

    /// Get text formatting at a specific position
    pub fn get_text_formatting_at_position(
        document: &EditableDocument,
        page: u32,
        position: usize,
    ) -> CommandResult<Option<TextFormat>> {
        let page_obj = document.get_page(page)
            .map_err(|e| CommandError::ExecutionFailed(e.to_string()))?;

        let mut current_position = 0;

        // Find the text object that contains this position
        for layer in page_obj.layers() {
            for object in &layer.objects {
                if let crate::document::PageObject::Text(text_obj) = object {
                    let text_len = text_obj.text.len();
                    if position >= current_position && position < current_position + text_len {
                        return Ok(Some(text_obj.format.clone()));
                    }
                    current_position += text_len + 1; // +1 for separator
                }
            }
        }

        // Return default format if position not found in any text object
        Ok(Some(TextFormat::default()))
    }

    /// Extract all text from a page
    pub fn extract_all_text_from_page(page: &crate::document::EditablePage) -> CommandResult<String> {
        // In a real implementation, this would:
        // 1. Iterate through all page layers
        // 2. Extract text from all text objects
        // 3. Maintain reading order
        // 4. Preserve formatting information

        // For now, return empty string
        Ok(String::new())
    }

    /// Extract text from a specific range
    pub fn extract_text_range(
        document: &EditableDocument,
        page: u32,
        start: usize,
        end: usize,
    ) -> CommandResult<String> {
        let page_content = get_page_text_content(document, page)?;

        if start > page_content.len() || end > page_content.len() || start > end {
            return Err(CommandError::InvalidParameters(
                format!("Invalid range: start={}, end={}, content_length={}", start, end, page_content.len())
            ));
        }

        // Extract the text in the specified range
        let extracted = page_content.chars()
            .skip(start)
            .take(end - start)
            .collect::<String>();

        Ok(extracted)
    }

    /// Delete text range from page
    pub fn delete_text_range_from_page(
        document: &mut EditableDocument,
        page: u32,
        start: usize,
        end: usize,
    ) -> CommandResult<()> {
        let page_obj = document.get_page_mut(page)
            .map_err(|e| CommandError::ExecutionFailed(e.to_string()))?;

        // Find text objects that contain the range and modify them
        let mut current_position = 0;
        let mut objects_to_modify = Vec::new();

        // First pass: identify which text objects need modification
        for layer in page_obj.layers() {
            for (layer_idx, object) in layer.objects.iter().enumerate() {
                if let crate::document::PageObject::Text(text_obj) = object {
                    let text_len = text_obj.text.len();
                    let obj_start = current_position;
                    let obj_end = current_position + text_len;

                    // Check if this object intersects with the deletion range
                    if obj_start < end && obj_end > start {
                        objects_to_modify.push((layer_idx, obj_start, obj_end, text_obj.clone()));
                    }

                    current_position = obj_end + 1; // +1 for separator
                }
            }
        }

        // Second pass: modify the text objects
        for (layer_idx, obj_start, obj_end, mut text_obj) in objects_to_modify {
            let delete_start = if start > obj_start { start - obj_start } else { 0 };
            let delete_end = if end < obj_end { end - obj_start } else { text_obj.text.len() };

            // Remove the text from this object
            if delete_start < text_obj.text.len() && delete_end <= text_obj.text.len() {
                let mut chars: Vec<char> = text_obj.text.chars().collect();
                chars.drain(delete_start..delete_end);
                text_obj.text = chars.into_iter().collect();

                // Update the text object in the page
                if let Some(layer) = page_obj.layers_mut().get_mut(0) { // Assuming content layer is first
                    if let Some(crate::document::PageObject::Text(obj)) = layer.objects.get_mut(layer_idx) {
                        obj.text = text_obj.text;
                    }
                }
            }
        }

        page_obj.mark_modified();
        Ok(())
    }

    /// Find or create text object at position
    pub fn find_or_create_text_object_at_position(
        page: &mut crate::document::EditablePage,
        position: usize,
    ) -> CommandResult<String> {
        let mut current_position = 0;

        // Find existing text object at position
        for layer in page.layers() {
            for object in &layer.objects {
                if let crate::document::PageObject::Text(text_obj) = object {
                    let text_len = text_obj.text.len();
                    if position >= current_position && position <= current_position + text_len {
                        return Ok(text_obj.id.clone());
                    }
                    current_position += text_len + 1; // +1 for separator
                }
            }
        }

        // Create new text object if none found
        let new_id = format!("text_{}", uuid::Uuid::new_v4());
        let text_obj = crate::document::TextObject {
            id: new_id.clone(),
            text: String::new(),
            bounds: crate::types::Rect::new(100.0, 100.0, 200.0, 20.0), // Default position
            format: crate::document::TextFormat::default(),
            alignment: crate::document::TextAlignment::Left,
        };

        // Add to content layer
        if let Some(content_layer) = page.layers_mut().iter_mut().find(|l| l.layer_type == crate::document::LayerType::Content) {
            content_layer.add_object(crate::document::PageObject::Text(text_obj));
        }

        Ok(new_id)
    }

    /// Insert text into a specific text object
    pub fn insert_text_in_object(
        page: &mut crate::document::EditablePage,
        text_object_id: &str,
        position: usize,
        text: &str,
        format: Option<TextFormat>,
    ) -> CommandResult<()> {
        // Find the text object and insert text
        for layer in page.layers_mut() {
            for object in &mut layer.objects {
                if let crate::document::PageObject::Text(text_obj) = object {
                    if text_obj.id == text_object_id {
                        // Calculate position within this object
                        let insert_pos = if position < text_obj.text.len() { position } else { text_obj.text.len() };

                        // Insert the text
                        text_obj.text.insert_str(insert_pos, text);

                        // Update formatting if provided
                        if let Some(fmt) = format {
                            text_obj.format = fmt;
                        }

                        page.mark_modified();
                        return Ok(());
                    }
                }
            }
        }

        Err(CommandError::ExecutionFailed(
            format!("Text object '{}' not found", text_object_id)
        ))
    }

    /// Update page layout after text changes
    pub fn update_page_layout(page: &mut crate::document::EditablePage) -> CommandResult<()> {
        // In a real implementation, this would:
        // 1. Recalculate text object bounds
        // 2. Update line breaks and wrapping
        // 3. Adjust object positions
        // 4. Update page content streams

        page.mark_modified();
        Ok(())
    }
}
