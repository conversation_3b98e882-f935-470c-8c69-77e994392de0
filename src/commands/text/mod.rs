/// Text editing commands
/// 
/// This module provides commands for text manipulation operations
/// including insertion, deletion, formatting, and movement.

pub mod insert_text;
pub mod delete_text;
pub mod format_text;
pub mod replace_text;

pub use insert_text::InsertTextCommand;
pub use delete_text::DeleteTextCommand;
pub use format_text::FormatTextCommand;
pub use replace_text::ReplaceTextCommand;

use super::*;
use crate::document::{EditableDocument, TextFormat, DocumentChange, ChangeType, ChangeState};

/// Text command utilities
pub mod utils {
    use super::*;
    
    /// Find text object at position on a page
    pub fn find_text_object_at_position(
        document: &EditableDocument,
        page: u32,
        position: usize,
    ) -> CommandResult<Option<String>> {
        let page_obj = document.get_page(page)
            .map_err(|e| CommandError::InvalidParameters(e.to_string()))?;
        
        // In a real implementation, this would search through text objects
        // For now, we'll return a placeholder
        Ok(Some("text_object_id".to_string()))
    }
    
    /// Get text content from a page
    pub fn get_page_text_content(
        document: &EditableDocument,
        page: u32,
    ) -> CommandResult<String> {
        let _page_obj = document.get_page(page)
            .map_err(|e| CommandError::InvalidParameters(e.to_string()))?;
        
        // In a real implementation, this would extract text from all text objects
        // For now, we'll return a placeholder
        Ok("Sample text content".to_string())
    }
    
    /// Validate text position within page content
    pub fn validate_text_position_in_page(
        document: &EditableDocument,
        page: u32,
        position: usize,
    ) -> CommandResult<()> {
        let text_content = get_page_text_content(document, page)?;
        
        if position > text_content.len() {
            return Err(CommandError::InvalidParameters(
                format!("Position {} exceeds text length {}", position, text_content.len())
            ));
        }
        
        Ok(())
    }
    
    /// Create a text change state
    pub fn create_text_change_state(
        content: String,
        position: usize,
        format: Option<TextFormat>,
    ) -> ChangeState {
        ChangeState::Text {
            content,
            position,
            format,
        }
    }
    
    /// Apply text formatting to a range
    pub fn apply_text_formatting(
        document: &mut EditableDocument,
        page: u32,
        start: usize,
        end: usize,
        format: TextFormat,
    ) -> CommandResult<()> {
        let _page_obj = document.get_page_mut(page)
            .map_err(|e| CommandError::ExecutionFailed(e.to_string()))?;
        
        // In a real implementation, this would apply formatting to text objects
        // For now, we'll just validate the parameters
        if start > end {
            return Err(CommandError::InvalidParameters(
                "Start position cannot be greater than end position".to_string()
            ));
        }
        
        Ok(())
    }
    
    /// Insert text at a specific position
    pub fn insert_text_at_position(
        document: &mut EditableDocument,
        page: u32,
        position: usize,
        text: &str,
        format: Option<TextFormat>,
    ) -> CommandResult<()> {
        let page_obj = document.get_page_mut(page)
            .map_err(|e| CommandError::ExecutionFailed(e.to_string()))?;

        // Validate the position
        validate_text_position_in_page(document, page, position)?;

        // Find or create text object at position
        let text_object_id = find_or_create_text_object_at_position(page_obj, position)?;

        // Insert the text with formatting
        insert_text_in_object(page_obj, &text_object_id, position, text, format)?;

        // Update page layout
        update_page_layout(page_obj)?;

        Ok(())
    }
    
    /// Delete text in a range
    pub fn delete_text_range(
        document: &mut EditableDocument,
        page: u32,
        start: usize,
        end: usize,
    ) -> CommandResult<String> {
        let _page_obj = document.get_page_mut(page)
            .map_err(|e| CommandError::ExecutionFailed(e.to_string()))?;
        
        // Validate range
        if start > end {
            return Err(CommandError::InvalidParameters(
                "Start position cannot be greater than end position".to_string()
            ));
        }
        
        validate_text_position_in_page(document, page, start)?;
        validate_text_position_in_page(document, page, end)?;
        
        // In a real implementation, this would:
        // 1. Find all text objects in the range
        // 2. Extract the text content
        // 3. Remove the text from the objects
        // 4. Merge text objects if necessary
        // 5. Update the page layout
        
        // Extract and delete the text
        let deleted_text = extract_text_range(document, page, start, end)?;
        delete_text_range_from_page(document, page, start, end)?;

        Ok(deleted_text)
    }
    
    /// Replace text in a range
    pub fn replace_text_range(
        document: &mut EditableDocument,
        page: u32,
        start: usize,
        end: usize,
        new_text: &str,
        format: Option<TextFormat>,
    ) -> CommandResult<String> {
        // Delete the old text and insert the new text
        let old_text = delete_text_range(document, page, start, end)?;
        insert_text_at_position(document, page, start, new_text, format)?;
        
        Ok(old_text)
    }
    
    /// Get text formatting at a position
    pub fn get_text_formatting_at_position(
        document: &EditableDocument,
        page: u32,
        position: usize,
    ) -> CommandResult<Option<TextFormat>> {
        let _page_obj = document.get_page(page)
            .map_err(|e| CommandError::InvalidParameters(e.to_string()))?;
        
        validate_text_position_in_page(document, page, position)?;
        
        // In a real implementation, this would find the text object at the position
        // and return its formatting
        Ok(None)
    }
    
    /// Calculate text bounds for layout
    pub fn calculate_text_bounds(
        text: &str,
        format: &TextFormat,
        max_width: Option<f32>,
    ) -> CommandResult<crate::types::Rect> {
        // In a real implementation, this would use font metrics to calculate
        // the actual text bounds
        let estimated_width = text.len() as f32 * format.font_size * 0.6; // Rough estimate
        let height = format.font_size * 1.2; // Line height
        
        let width = if let Some(max_w) = max_width {
            estimated_width.min(max_w)
        } else {
            estimated_width
        };
        
        Ok(crate::types::Rect::new(0.0, 0.0, width, height))
    }
}
