/// Insert text command implementation
/// 
/// This command handles text insertion at a specific position
/// with optional formatting.

use super::*;
use crate::commands::{Command, CommandMetadata, CommandResult, CommandError, ExecutionContext, ValidationResult};
use crate::document::{TextFormat, DocumentChange, ChangeType, ChangeState};
use crate::impl_command_base;

use std::any::Any;

/// Command for inserting text at a specific position
#[derive(Debug, Clone)]
pub struct InsertTextCommand {
    base: crate::commands::base::BaseCommand,
    page: u32,
    position: usize,
    text: String,
    format: Option<TextFormat>,
    previous_state: Option<ChangeState>,
}

impl InsertTextCommand {
    /// Create a new insert text command
    pub fn new(
        page: u32,
        position: usize,
        text: String,
        format: Option<TextFormat>,
    ) -> Self {
        let metadata = CommandMetadata::new(
            "InsertText".to_string(),
            format!("Insert '{}' at position {} on page {}", 
                    if text.len() > 20 { 
                        format!("{}...", &text[..20]) 
                    } else { 
                        text.clone() 
                    }, 
                    position, 
                    page)
        );
        
        Self {
            base: crate::commands::base::BaseCommand::new(metadata),
            page,
            position,
            text,
            format,
            previous_state: None,
        }
    }
    
    /// Get the page number
    pub fn page(&self) -> u32 {
        self.page
    }
    
    /// Get the insertion position
    pub fn position(&self) -> usize {
        self.position
    }
    
    /// Get the text to insert
    pub fn text(&self) -> &str {
        &self.text
    }
    
    /// Get the text format
    pub fn format(&self) -> Option<&TextFormat> {
        self.format.as_ref()
    }
    
    /// Set the text format
    pub fn set_format(&mut self, format: Option<TextFormat>) {
        self.format = format;
    }
    
    /// Check if this command can be merged with another insert command
    pub fn can_merge_with_insert(&self, other: &InsertTextCommand) -> bool {
        // Can merge if:
        // 1. Same page
        // 2. Adjacent positions
        // 3. Same formatting
        // 4. Both are single character insertions (for typing)
        self.page == other.page &&
        self.position + self.text.len() == other.position &&
        self.format == other.format &&
        self.text.len() == 1 &&
        other.text.len() == 1
    }
    
    /// Merge with another insert command
    pub fn merge_with_insert(&mut self, other: InsertTextCommand) -> CommandResult<()> {
        if !self.can_merge_with_insert(&other) {
            return Err(CommandError::ExecutionFailed(
                "Cannot merge incompatible insert commands".to_string()
            ));
        }
        
        self.text.push_str(&other.text);
        Ok(())
    }
}

impl_command_base!(InsertTextCommand);

impl Command for InsertTextCommand {
    fn validate(&self, context: &ExecutionContext) -> ValidationResult {
        let mut result = ValidationResult::valid();
        
        // Validate page number
        if let Err(e) = crate::commands::utils::validate_page_number(self.page, &context.document) {
            result.add_error(e.to_string());
        }
        
        // Validate text position
        if let Err(e) = super::utils::validate_text_position_in_page(&context.document, self.page, self.position) {
            result.add_error(e.to_string());
        }
        
        // Validate text content
        if self.text.is_empty() {
            result.add_warning("Inserting empty text has no effect".to_string());
        }
        
        // Check if text contains only whitespace
        if self.text.trim().is_empty() && !self.text.is_empty() {
            result.add_warning("Inserting only whitespace".to_string());
        }
        
        // Validate text length
        if self.text.len() > 10000 {
            result.add_warning("Inserting very long text may impact performance".to_string());
        }
        
        // Check permissions
        if let Err(e) = crate::commands::utils::check_permission(&context.document, "edit_text") {
            result.add_error(e.to_string());
        }
        
        result
    }
    
    fn execute(&mut self, context: &mut ExecutionContext) -> CommandResult<()> {
        // Store the previous state for undo
        let previous_content = super::utils::get_page_text_content(&context.document, self.page)?;
        let previous_format = super::utils::get_text_formatting_at_position(
            &context.document, 
            self.page, 
            self.position
        )?;
        
        self.previous_state = Some(super::utils::create_text_change_state(
            previous_content,
            self.position,
            previous_format,
        ));
        
        // Execute the text insertion
        super::utils::insert_text_at_position(
            &mut context.document,
            self.page,
            self.position,
            &self.text,
            self.format.clone(),
        )?;
        
        // Create and record the document change
        let new_content = super::utils::get_page_text_content(&context.document, self.page)?;
        let new_state = super::utils::create_text_change_state(
            new_content,
            self.position + self.text.len(),
            self.format.clone(),
        );
        
        let change = DocumentChange::new(
            ChangeType::TextInsert,
            Some(self.page),
            format!("Insert '{}' at position {}", self.text, self.position),
            self.previous_state.clone().unwrap(),
            new_state,
        );
        
        // Add user information if available
        let change = if let Some(ref user) = context.user_info {
            change.with_author(user.name.clone())
        } else {
            change
        };
        
        self.set_change(change.clone());
        context.record_change(change);
        
        self.base.set_executed(true);
        Ok(())
    }
    
    fn undo(&mut self, context: &mut ExecutionContext) -> CommandResult<()> {
        if !self.base.is_executed() {
            return Err(CommandError::UndoRedoFailed(
                "Command has not been executed".to_string()
            ));
        }
        
        if self.previous_state.is_none() {
            return Err(CommandError::UndoRedoFailed(
                "No previous state available for undo".to_string()
            ));
        }
        
        // Remove the inserted text
        super::utils::delete_text_range(
            &mut context.document,
            self.page,
            self.position,
            self.position + self.text.len(),
        )?;
        
        self.base.set_executed(false);
        Ok(())
    }
    
    fn redo(&mut self, context: &mut ExecutionContext) -> CommandResult<()> {
        if self.base.is_executed() {
            return Err(CommandError::UndoRedoFailed(
                "Command is already executed".to_string()
            ));
        }
        
        // Re-execute the command
        self.execute(context)
    }
    
    fn can_merge_with(&self, other: &dyn Command) -> bool {
        if let Some(other_insert) = other.as_any().downcast_ref::<InsertTextCommand>() {
            self.can_merge_with_insert(other_insert)
        } else {
            false
        }
    }
    
    fn merge_with(&mut self, other: Box<dyn Command>) -> CommandResult<()> {
        if let Ok(other_insert) = other.as_any().downcast::<InsertTextCommand>() {
            self.merge_with_insert(*other_insert)
        } else {
            Err(CommandError::ExecutionFailed(
                "Cannot merge with incompatible command type".to_string()
            ))
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::document::{EditableDocument, EditingConfig, DocumentMetadata};
    use crate::types::DocumentInfo;
    
    fn create_test_document() -> EditableDocument {
        let doc_info = DocumentInfo::new(
            1,
            Some("Test Document".to_string()),
            None, None, None, None, None, None,
            false,
            crate::types::DocumentPermissions {
                can_print: true,
                can_modify: true,
                can_copy: true,
                can_add_notes: true,
                can_fill_forms: true,
                can_extract_for_accessibility: true,
                can_assemble: true,
                can_print_high_quality: true,
            }
        );
        
        EditableDocument::new(
            doc_info,
            vec![],
            EditingConfig::default(),
        ).unwrap()
    }
    
    #[test]
    fn test_insert_text_command_creation() {
        let command = InsertTextCommand::new(
            1,
            0,
            "Hello, World!".to_string(),
            None,
        );
        
        assert_eq!(command.page(), 1);
        assert_eq!(command.position(), 0);
        assert_eq!(command.text(), "Hello, World!");
        assert!(command.format().is_none());
    }
    
    #[test]
    fn test_insert_text_command_validation() {
        let document = create_test_document();
        let context = ExecutionContext::new(document);
        
        let command = InsertTextCommand::new(
            1,
            0,
            "Hello".to_string(),
            None,
        );
        
        let validation = command.validate(&context);
        assert!(validation.is_valid);
    }
    
    #[test]
    fn test_insert_text_command_merge() {
        let mut cmd1 = InsertTextCommand::new(1, 0, "H".to_string(), None);
        let cmd2 = InsertTextCommand::new(1, 1, "e".to_string(), None);
        
        assert!(cmd1.can_merge_with_insert(&cmd2));
        assert!(cmd1.merge_with_insert(cmd2).is_ok());
        assert_eq!(cmd1.text(), "He");
    }
}
