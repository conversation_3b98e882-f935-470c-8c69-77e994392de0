/// Base command trait and execution context
/// 
/// This module defines the core command interface that all editing
/// operations must implement for undo/redo functionality.

use super::*;
use crate::document::{EditableDocument, DocumentChange};
use crate::error::PDFResult;

use std::any::Any;
use std::fmt::Debug;

/// Core command trait that all editing operations must implement
pub trait Command: Debug + Send + Sync {
    /// Get command metadata
    fn metadata(&self) -> &CommandMetadata;
    
    /// Validate the command before execution
    fn validate(&self, context: &ExecutionContext) -> ValidationResult;
    
    /// Execute the command
    fn execute(&mut self, context: &mut ExecutionContext) -> CommandResult<()>;
    
    /// Undo the command (reverse its effects)
    fn undo(&mut self, context: &mut ExecutionContext) -> CommandResult<()>;
    
    /// Redo the command (re-apply its effects)
    fn redo(&mut self, context: &mut ExecutionContext) -> CommandResult<()> {
        // Default implementation just calls execute again
        self.execute(context)
    }
    
    /// Get the document change that this command represents
    fn get_change(&self) -> Option<&DocumentChange>;
    
    /// Set the document change for this command
    fn set_change(&mut self, change: DocumentChange);
    
    /// Check if the command can be merged with another command
    fn can_merge_with(&self, other: &dyn Command) -> bool {
        false // Default: commands cannot be merged
    }
    
    /// Merge this command with another command
    fn merge_with(&mut self, other: Box<dyn Command>) -> CommandResult<()> {
        Err(CommandError::ExecutionFailed(
            "Command merging not supported".to_string()
        ))
    }
    
    /// Get command as Any for downcasting
    fn as_any(&self) -> &dyn Any;
    
    /// Get mutable command as Any for downcasting
    fn as_any_mut(&mut self) -> &mut dyn Any;
    
    /// Clone the command
    fn clone_command(&self) -> Box<dyn Command>;
}

/// Execution context for commands
#[derive(Debug)]
pub struct ExecutionContext {
    /// The document being edited
    pub document: EditableDocument,
    /// Current user/editor information
    pub user_info: Option<UserInfo>,
    /// Execution timestamp
    pub timestamp: u64,
    /// Execution mode
    pub execution_mode: ExecutionMode,
    /// Whether to record changes for undo/redo
    pub record_changes: bool,
    /// Additional context data
    pub context_data: std::collections::HashMap<String, String>,
}

impl ExecutionContext {
    /// Create a new execution context
    pub fn new(document: EditableDocument) -> Self {
        Self {
            document,
            user_info: None,
            timestamp: js_sys::Date::now() as u64,
            execution_mode: ExecutionMode::Immediate,
            record_changes: true,
            context_data: std::collections::HashMap::new(),
        }
    }
    
    /// Set user information
    pub fn with_user(mut self, user_info: UserInfo) -> Self {
        self.user_info = Some(user_info);
        self
    }
    
    /// Set execution mode
    pub fn with_execution_mode(mut self, mode: ExecutionMode) -> Self {
        self.execution_mode = mode;
        self
    }
    
    /// Disable change recording
    pub fn without_change_recording(mut self) -> Self {
        self.record_changes = false;
        self
    }
    
    /// Add context data
    pub fn with_context_data(mut self, key: String, value: String) -> Self {
        self.context_data.insert(key, value);
        self
    }
    
    /// Get context data
    pub fn get_context_data(&self, key: &str) -> Option<&String> {
        self.context_data.get(key)
    }
    
    /// Record a document change
    pub fn record_change(&mut self, change: DocumentChange) {
        if self.record_changes {
            self.document.change_tracker_mut().record_change(change);
        }
    }
}

/// User information for command execution
#[derive(Debug, Clone)]
pub struct UserInfo {
    pub name: String,
    pub email: Option<String>,
    pub role: UserRole,
    pub permissions: Vec<String>,
}

/// User role enumeration
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum UserRole {
    Viewer,
    Editor,
    Reviewer,
    Administrator,
}

/// Base implementation for simple commands
#[derive(Debug)]
pub struct BaseCommand {
    metadata: CommandMetadata,
    change: Option<DocumentChange>,
    executed: bool,
}

impl BaseCommand {
    pub fn new(metadata: CommandMetadata) -> Self {
        Self {
            metadata,
            change: None,
            executed: false,
        }
    }
    
    pub fn is_executed(&self) -> bool {
        self.executed
    }
    
    pub fn set_executed(&mut self, executed: bool) {
        self.executed = executed;
    }
}

/// Macro for implementing the Command trait for simple commands
#[macro_export]
macro_rules! impl_command_base {
    ($command_type:ty) => {
        impl Command for $command_type {
            fn metadata(&self) -> &CommandMetadata {
                &self.base.metadata
            }
            
            fn get_change(&self) -> Option<&DocumentChange> {
                self.base.change.as_ref()
            }
            
            fn set_change(&mut self, change: DocumentChange) {
                self.base.change = Some(change);
            }
            
            fn as_any(&self) -> &dyn Any {
                self
            }
            
            fn as_any_mut(&mut self) -> &mut dyn Any {
                self
            }
            
            fn clone_command(&self) -> Box<dyn Command> {
                Box::new(self.clone())
            }
        }
    };
}

/// Composite command for executing multiple commands as a single operation
#[derive(Debug)]
pub struct CompositeCommand {
    base: BaseCommand,
    commands: Vec<Box<dyn Command>>,
    execution_order: Vec<usize>,
}

impl CompositeCommand {
    pub fn new(name: String, description: String) -> Self {
        let metadata = CommandMetadata::new(name, description);
        Self {
            base: BaseCommand::new(metadata),
            commands: Vec::new(),
            execution_order: Vec::new(),
        }
    }
    
    /// Add a command to the composite
    pub fn add_command(&mut self, command: Box<dyn Command>) {
        self.commands.push(command);
        self.execution_order.push(self.commands.len() - 1);
    }
    
    /// Add multiple commands
    pub fn add_commands(&mut self, commands: Vec<Box<dyn Command>>) {
        for command in commands {
            self.add_command(command);
        }
    }
    
    /// Get the number of sub-commands
    pub fn command_count(&self) -> usize {
        self.commands.len()
    }
}

impl Command for CompositeCommand {
    fn metadata(&self) -> &CommandMetadata {
        self.base.metadata()
    }
    
    fn validate(&self, context: &ExecutionContext) -> ValidationResult {
        let mut result = ValidationResult::valid();
        
        for command in &self.commands {
            let validation = command.validate(context);
            if !validation.is_valid {
                for error in validation.errors {
                    result.add_error(error);
                }
            }
            for warning in validation.warnings {
                result.add_warning(warning);
            }
        }
        
        result
    }
    
    fn execute(&mut self, context: &mut ExecutionContext) -> CommandResult<()> {
        // Execute commands in order
        for &index in &self.execution_order {
            if let Some(command) = self.commands.get_mut(index) {
                command.execute(context)?;
            }
        }
        
        self.base.set_executed(true);
        Ok(())
    }
    
    fn undo(&mut self, context: &mut ExecutionContext) -> CommandResult<()> {
        // Undo commands in reverse order
        for &index in self.execution_order.iter().rev() {
            if let Some(command) = self.commands.get_mut(index) {
                command.undo(context)?;
            }
        }
        
        self.base.set_executed(false);
        Ok(())
    }
    
    fn get_change(&self) -> Option<&DocumentChange> {
        self.base.get_change()
    }
    
    fn set_change(&mut self, change: DocumentChange) {
        self.base.set_change(change);
    }
    
    fn as_any(&self) -> &dyn Any {
        self
    }
    
    fn as_any_mut(&mut self) -> &mut dyn Any {
        self
    }
    
    fn clone_command(&self) -> Box<dyn Command> {
        let mut cloned = CompositeCommand::new(
            self.metadata().name.clone(),
            self.metadata().description.clone(),
        );
        
        for command in &self.commands {
            cloned.add_command(command.clone_command());
        }
        
        Box::new(cloned)
    }
}

/// Conditional command that executes based on a condition
#[derive(Debug)]
pub struct ConditionalCommand {
    base: BaseCommand,
    condition: Box<dyn Fn(&ExecutionContext) -> bool + Send + Sync>,
    command: Option<Box<dyn Command>>,
}

impl ConditionalCommand {
    pub fn new<F>(
        name: String,
        description: String,
        condition: F,
        command: Box<dyn Command>,
    ) -> Self
    where
        F: Fn(&ExecutionContext) -> bool + Send + Sync + 'static,
    {
        let metadata = CommandMetadata::new(name, description);
        Self {
            base: BaseCommand::new(metadata),
            condition: Box::new(condition),
            command: Some(command),
        }
    }
}

impl Command for ConditionalCommand {
    fn metadata(&self) -> &CommandMetadata {
        self.base.metadata()
    }
    
    fn validate(&self, context: &ExecutionContext) -> ValidationResult {
        if (self.condition)(context) {
            if let Some(ref command) = self.command {
                command.validate(context)
            } else {
                ValidationResult::valid()
            }
        } else {
            ValidationResult::valid()
        }
    }
    
    fn execute(&mut self, context: &mut ExecutionContext) -> CommandResult<()> {
        if (self.condition)(context) {
            if let Some(ref mut command) = self.command {
                command.execute(context)?;
            }
        }
        
        self.base.set_executed(true);
        Ok(())
    }
    
    fn undo(&mut self, context: &mut ExecutionContext) -> CommandResult<()> {
        if self.base.is_executed() {
            if let Some(ref mut command) = self.command {
                command.undo(context)?;
            }
        }
        
        self.base.set_executed(false);
        Ok(())
    }
    
    fn get_change(&self) -> Option<&DocumentChange> {
        self.base.get_change()
    }
    
    fn set_change(&mut self, change: DocumentChange) {
        self.base.set_change(change);
    }
    
    fn as_any(&self) -> &dyn Any {
        self
    }
    
    fn as_any_mut(&mut self) -> &mut dyn Any {
        self
    }
    
    fn clone_command(&self) -> Box<dyn Command> {
        // Note: This is a simplified clone that doesn't clone the condition function
        // In a real implementation, you might need a different approach for cloning
        // functions or make the command non-cloneable
        panic!("ConditionalCommand cannot be cloned due to function pointer")
    }
}
