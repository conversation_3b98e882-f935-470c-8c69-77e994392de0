/// Move page command implementation
/// 
/// This command handles page reordering within the document.

use super::*;
use crate::commands::{Command, CommandMetadata, CommandResult, CommandError, ExecutionContext, ValidationResult};
use crate::document::{DocumentChange, ChangeType, ChangeState};
use crate::impl_command_base;

use std::any::Any;

/// Command for moving a page to a new position
#[derive(Debug, <PERSON>lone)]
pub struct MovePageCommand {
    base: crate::commands::base::BaseCommand,
    from_page: u32,
    to_position: u32,
    previous_state: Option<ChangeState>,
}

impl MovePageCommand {
    /// Create a new move page command
    pub fn new(from_page: u32, to_position: u32) -> Self {
        let metadata = CommandMetadata::new(
            "MovePage".to_string(),
            format!("Move page {} to position {}", from_page, to_position)
        );
        
        Self {
            base: crate::commands::base::BaseCommand::new(metadata),
            from_page,
            to_position,
            previous_state: None,
        }
    }
    
    /// Get the source page number
    pub fn from_page(&self) -> u32 {
        self.from_page
    }
    
    /// Get the destination position
    pub fn to_position(&self) -> u32 {
        self.to_position
    }
}

impl_command_base!(MovePageCommand);

impl Command for MovePageCommand {
    fn validate(&self, context: &ExecutionContext) -> ValidationResult {
        let mut result = ValidationResult::valid();
        
        // Validate the move operation
        if let Err(e) = super::utils::validate_page_move(&context.document, self.from_page, self.to_position) {
            result.add_error(e.to_string());
        }
        
        // Check permissions
        if let Err(e) = crate::commands::utils::check_permission(&context.document, "edit_pages") {
            result.add_error(e.to_string());
        }
        
        result
    }
    
    fn execute(&mut self, context: &mut ExecutionContext) -> CommandResult<()> {
        // Get page information before move
        let (page_settings, exists) = super::utils::get_page_info(&context.document, self.from_page)?;
        
        if !exists {
            return Err(CommandError::ExecutionFailed(
                format!("Page {} does not exist", self.from_page)
            ));
        }
        
        // Store previous state for undo
        self.previous_state = Some(super::utils::create_page_change_state(
            self.from_page,
            page_settings.clone(),
            true,
        ));
        
        // Execute the page move
        context.document.move_page(self.from_page, self.to_position)
            .map_err(|e| CommandError::ExecutionFailed(e.to_string()))?;
        
        // Create and record the document change
        let new_state = super::utils::create_page_change_state(
            self.to_position,
            page_settings,
            true,
        );
        
        let change = DocumentChange::new(
            ChangeType::PageMove,
            Some(self.from_page),
            format!("Move page {} to position {}", self.from_page, self.to_position),
            self.previous_state.clone().unwrap(),
            new_state,
        );
        
        // Add user information if available
        let change = if let Some(ref user) = context.user_info {
            change.with_author(user.name.clone())
        } else {
            change
        };
        
        self.set_change(change.clone());
        context.record_change(change);
        
        self.base.set_executed(true);
        Ok(())
    }
    
    fn undo(&mut self, context: &mut ExecutionContext) -> CommandResult<()> {
        if !self.base.is_executed() {
            return Err(CommandError::UndoRedoFailed(
                "Command has not been executed".to_string()
            ));
        }
        
        // Move the page back to its original position
        context.document.move_page(self.to_position, self.from_page)
            .map_err(|e| CommandError::UndoRedoFailed(e.to_string()))?;
        
        self.base.set_executed(false);
        Ok(())
    }
    
    fn redo(&mut self, context: &mut ExecutionContext) -> CommandResult<()> {
        if self.base.is_executed() {
            return Err(CommandError::UndoRedoFailed(
                "Command is already executed".to_string()
            ));
        }
        
        // Re-execute the command
        self.execute(context)
    }
}
