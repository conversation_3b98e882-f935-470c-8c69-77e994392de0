/// Delete page command implementation
/// 
/// This command handles page deletion with proper validation.

use super::*;
use crate::commands::{Command, CommandMetadata, CommandResult, CommandError, ExecutionContext, ValidationResult};
use crate::document::{PageSettings, DocumentChange, ChangeType, ChangeState};
use crate::impl_command_base;

use std::any::Any;

/// Command for deleting a page
#[derive(Debug, <PERSON>lone)]
pub struct DeletePageCommand {
    base: crate::commands::base::BaseCommand,
    page: u32,
    deleted_page_settings: Option<PageSettings>,
    previous_state: Option<ChangeState>,
}

impl DeletePageCommand {
    /// Create a new delete page command
    pub fn new(page: u32) -> Self {
        let metadata = CommandMetadata::new(
            "DeletePage".to_string(),
            format!("Delete page {}", page)
        );
        
        Self {
            base: crate::commands::base::BaseCommand::new(metadata),
            page,
            deleted_page_settings: None,
            previous_state: None,
        }
    }
    
    /// Get the page number to delete
    pub fn page(&self) -> u32 {
        self.page
    }
    
    /// Get the deleted page settings (available after execution)
    pub fn deleted_page_settings(&self) -> Option<&PageSettings> {
        self.deleted_page_settings.as_ref()
    }
}

impl_command_base!(DeletePageCommand);

impl Command for DeletePageCommand {
    fn validate(&self, context: &ExecutionContext) -> ValidationResult {
        let mut result = ValidationResult::valid();
        
        // Check if page can be deleted
        if let Err(e) = super::utils::can_delete_page(&context.document, self.page) {
            result.add_error(e.to_string());
        }
        
        // Check permissions
        if let Err(e) = crate::commands::utils::check_permission(&context.document, "edit_pages") {
            result.add_error(e.to_string());
        }
        
        // Warning for deleting pages with content
        // In a real implementation, we would check if the page has content
        result.add_warning("Deleting a page will permanently remove all its content".to_string());
        
        result
    }
    
    fn execute(&mut self, context: &mut ExecutionContext) -> CommandResult<()> {
        // Get page information before deletion
        let (page_settings, exists) = super::utils::get_page_info(&context.document, self.page)?;
        
        if !exists {
            return Err(CommandError::ExecutionFailed(
                format!("Page {} does not exist", self.page)
            ));
        }
        
        // Store previous state for undo
        self.previous_state = Some(super::utils::create_page_change_state(
            self.page,
            page_settings.clone(),
            true, // Page exists
        ));
        
        self.deleted_page_settings = Some(page_settings.clone());
        
        // Delete the page
        context.document.remove_page(self.page)
            .map_err(|e| CommandError::ExecutionFailed(e.to_string()))?;
        
        // Create and record the document change
        let new_state = super::utils::create_page_change_state(
            self.page,
            page_settings,
            false, // Page no longer exists
        );
        
        let change = DocumentChange::new(
            ChangeType::PageDelete,
            Some(self.page),
            format!("Delete page {}", self.page),
            self.previous_state.clone().unwrap(),
            new_state,
        );
        
        // Add user information if available
        let change = if let Some(ref user) = context.user_info {
            change.with_author(user.name.clone())
        } else {
            change
        };
        
        self.set_change(change.clone());
        context.record_change(change);
        
        self.base.set_executed(true);
        Ok(())
    }
    
    fn undo(&mut self, context: &mut ExecutionContext) -> CommandResult<()> {
        if !self.base.is_executed() {
            return Err(CommandError::UndoRedoFailed(
                "Command has not been executed".to_string()
            ));
        }
        
        let page_settings = self.deleted_page_settings.as_ref()
            .ok_or_else(|| CommandError::UndoRedoFailed(
                "No deleted page settings available for undo".to_string()
            ))?;
        
        // Re-insert the page at the same position
        let restored_page_number = context.document.insert_page(self.page, Some(page_settings.clone()))
            .map_err(|e| CommandError::UndoRedoFailed(e.to_string()))?;
        
        // Verify the page was restored at the correct position
        if restored_page_number != self.page {
            return Err(CommandError::UndoRedoFailed(
                format!("Page restored at position {} instead of {}", restored_page_number, self.page)
            ));
        }
        
        self.base.set_executed(false);
        Ok(())
    }
    
    fn redo(&mut self, context: &mut ExecutionContext) -> CommandResult<()> {
        if self.base.is_executed() {
            return Err(CommandError::UndoRedoFailed(
                "Command is already executed".to_string()
            ));
        }
        
        // Re-execute the command
        self.execute(context)
    }
}
