/// Duplicate page command implementation
/// 
/// This command handles page duplication with content copying.

use super::*;
use crate::commands::{Command, CommandMetadata, CommandResult, CommandError, ExecutionContext, ValidationResult};
use crate::document::{DocumentChange, ChangeType, ChangeState};
use crate::impl_command_base;

use std::any::Any;

/// Command for duplicating a page
#[derive(Debug, Clone)]
pub struct DuplicatePageCommand {
    base: crate::commands::base::BaseCommand,
    source_page: u32,
    insert_after: bool,
    duplicated_page_number: Option<u32>,
    previous_state: Option<ChangeState>,
}

impl DuplicatePageCommand {
    /// Create a new duplicate page command
    pub fn new(source_page: u32, insert_after: bool) -> Self {
        let metadata = CommandMetadata::new(
            "DuplicatePage".to_string(),
            format!("Duplicate page {} {}", 
                    source_page, 
                    if insert_after { "after" } else { "before" })
        );
        
        Self {
            base: crate::commands::base::BaseCommand::new(metadata),
            source_page,
            insert_after,
            duplicated_page_number: None,
            previous_state: None,
        }
    }
    
    /// Get the source page number
    pub fn source_page(&self) -> u32 {
        self.source_page
    }
    
    /// Check if inserting after the source page
    pub fn insert_after(&self) -> bool {
        self.insert_after
    }
    
    /// Get the duplicated page number (available after execution)
    pub fn duplicated_page_number(&self) -> Option<u32> {
        self.duplicated_page_number
    }
}

impl_command_base!(DuplicatePageCommand);

impl Command for DuplicatePageCommand {
    fn validate(&self, context: &ExecutionContext) -> ValidationResult {
        let mut result = ValidationResult::valid();
        
        // Validate the duplication operation
        if let Err(e) = super::utils::validate_page_duplication(&context.document, self.source_page, self.insert_after) {
            result.add_error(e.to_string());
        }
        
        // Check permissions
        if let Err(e) = crate::commands::utils::check_permission(&context.document, "edit_pages") {
            result.add_error(e.to_string());
        }
        
        result
    }
    
    fn execute(&mut self, context: &mut ExecutionContext) -> CommandResult<()> {
        // Validate and get insertion position
        let insert_position = super::utils::validate_page_duplication(
            &context.document, 
            self.source_page, 
            self.insert_after
        )?;
        
        // Get source page information
        let (page_settings, exists) = super::utils::get_page_info(&context.document, self.source_page)?;
        
        if !exists {
            return Err(CommandError::ExecutionFailed(
                format!("Source page {} does not exist", self.source_page)
            ));
        }
        
        // Store previous state for undo
        self.previous_state = Some(super::utils::create_page_change_state(
            insert_position,
            page_settings.clone(),
            false, // New page doesn't exist yet
        ));
        
        // Duplicate the page
        let duplicated_page_number = context.document.duplicate_page(self.source_page, self.insert_after)
            .map_err(|e| CommandError::ExecutionFailed(e.to_string()))?;
        
        self.duplicated_page_number = Some(duplicated_page_number);
        
        // Create and record the document change
        let new_state = super::utils::create_page_change_state(
            duplicated_page_number,
            page_settings,
            true, // Page now exists
        );
        
        let change = DocumentChange::new(
            ChangeType::PageInsert, // Duplication is essentially an insertion
            Some(duplicated_page_number),
            format!("Duplicate page {} to position {}", self.source_page, duplicated_page_number),
            self.previous_state.clone().unwrap(),
            new_state,
        );
        
        // Add user information if available
        let change = if let Some(ref user) = context.user_info {
            change.with_author(user.name.clone())
        } else {
            change
        };
        
        self.set_change(change.clone());
        context.record_change(change);
        
        self.base.set_executed(true);
        Ok(())
    }
    
    fn undo(&mut self, context: &mut ExecutionContext) -> CommandResult<()> {
        if !self.base.is_executed() {
            return Err(CommandError::UndoRedoFailed(
                "Command has not been executed".to_string()
            ));
        }
        
        let duplicated_page_number = self.duplicated_page_number
            .ok_or_else(|| CommandError::UndoRedoFailed(
                "No duplicated page number available for undo".to_string()
            ))?;
        
        // Remove the duplicated page
        context.document.remove_page(duplicated_page_number)
            .map_err(|e| CommandError::UndoRedoFailed(e.to_string()))?;
        
        self.base.set_executed(false);
        Ok(())
    }
    
    fn redo(&mut self, context: &mut ExecutionContext) -> CommandResult<()> {
        if self.base.is_executed() {
            return Err(CommandError::UndoRedoFailed(
                "Command is already executed".to_string()
            ));
        }
        
        // Re-execute the command
        self.execute(context)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::document::{EditableDocument, EditingConfig};
    use crate::types::DocumentInfo;
    
    fn create_test_document() -> EditableDocument {
        let doc_info = DocumentInfo::new(
            2, // Start with 2 pages
            Some("Test Document".to_string()),
            None, None, None, None, None, None,
            false,
            crate::types::DocumentPermissions {
                can_print: true,
                can_modify: true,
                can_copy: true,
                can_add_notes: true,
                can_fill_forms: true,
                can_extract_for_accessibility: true,
                can_assemble: true,
                can_print_high_quality: true,
            }
        );
        
        EditableDocument::new(
            doc_info,
            vec![],
            EditingConfig::default(),
        ).unwrap()
    }
    
    #[test]
    fn test_duplicate_page_command_creation() {
        let command = DuplicatePageCommand::new(1, true);
        
        assert_eq!(command.source_page(), 1);
        assert!(command.insert_after());
        assert!(command.duplicated_page_number().is_none());
    }
    
    #[test]
    fn test_duplicate_page_command_validation() {
        let document = create_test_document();
        let context = ExecutionContext::new(document);
        
        let command = DuplicatePageCommand::new(1, true);
        let validation = command.validate(&context);
        assert!(validation.is_valid);
    }
    
    #[test]
    fn test_duplicate_page_insert_before() {
        let command = DuplicatePageCommand::new(2, false);
        
        assert_eq!(command.source_page(), 2);
        assert!(!command.insert_after());
    }
}
