/// Insert page command implementation
/// 
/// This command handles page insertion at a specified position.

use super::*;
use crate::commands::{Command, CommandMetadata, CommandResult, CommandError, ExecutionContext, ValidationResult};
use crate::document::{PageSettings, DocumentChange, ChangeType, ChangeState};
use crate::impl_command_base;

use std::any::Any;

/// Command for inserting a new page
#[derive(Debu<PERSON>, Clone)]
pub struct InsertPageCommand {
    base: crate::commands::base::BaseCommand,
    position: u32,
    settings: Option<PageSettings>,
    inserted_page_number: Option<u32>,
    previous_state: Option<ChangeState>,
}

impl InsertPageCommand {
    /// Create a new insert page command
    pub fn new(position: u32, settings: Option<PageSettings>) -> Self {
        let metadata = CommandMetadata::new(
            "InsertPage".to_string(),
            format!("Insert new page at position {}", position)
        );
        
        Self {
            base: crate::commands::base::BaseCommand::new(metadata),
            position,
            settings,
            inserted_page_number: None,
            previous_state: None,
        }
    }
    
    /// Get the insertion position
    pub fn position(&self) -> u32 {
        self.position
    }
    
    /// Get the page settings
    pub fn settings(&self) -> Option<&PageSettings> {
        self.settings.as_ref()
    }
    
    /// Get the inserted page number (available after execution)
    pub fn inserted_page_number(&self) -> Option<u32> {
        self.inserted_page_number
    }
    
    /// Set custom page settings
    pub fn with_settings(mut self, settings: PageSettings) -> Self {
        self.settings = Some(settings);
        self
    }
}

impl_command_base!(InsertPageCommand);

impl Command for InsertPageCommand {
    fn validate(&self, context: &ExecutionContext) -> ValidationResult {
        let mut result = ValidationResult::valid();
        
        // Validate insertion position
        if let Err(e) = super::utils::validate_page_insertion_position(&context.document, self.position) {
            result.add_error(e.to_string());
        }
        
        // Validate page settings if provided
        if let Some(ref settings) = self.settings {
            if settings.width <= 0.0 || settings.height <= 0.0 {
                result.add_error("Page dimensions must be positive".to_string());
            }
            
            if settings.margin_left + settings.margin_right >= settings.width {
                result.add_error("Horizontal margins exceed page width".to_string());
            }
            
            if settings.margin_top + settings.margin_bottom >= settings.height {
                result.add_error("Vertical margins exceed page height".to_string());
            }
            
            // Check for reasonable page sizes
            if settings.width > 5000.0 || settings.height > 5000.0 {
                result.add_warning("Very large page size may cause performance issues".to_string());
            }
            
            if settings.width < 50.0 || settings.height < 50.0 {
                result.add_warning("Very small page size may cause layout issues".to_string());
            }
        }
        
        // Check permissions
        if let Err(e) = crate::commands::utils::check_permission(&context.document, "edit_pages") {
            result.add_error(e.to_string());
        }
        
        result
    }
    
    fn execute(&mut self, context: &mut ExecutionContext) -> CommandResult<()> {
        // Get page settings (use provided or default)
        let page_settings = self.settings.clone()
            .unwrap_or_else(|| super::utils::get_default_page_settings(&context.document));
        
        // Store previous state for undo
        self.previous_state = Some(super::utils::create_page_change_state(
            self.position,
            page_settings.clone(),
            false, // Page doesn't exist yet
        ));
        
        // Insert the page
        let inserted_page_number = context.document.insert_page(self.position, Some(page_settings.clone()))
            .map_err(|e| CommandError::ExecutionFailed(e.to_string()))?;
        
        self.inserted_page_number = Some(inserted_page_number);
        
        // Create and record the document change
        let new_state = super::utils::create_page_change_state(
            inserted_page_number,
            page_settings,
            true, // Page now exists
        );
        
        let change = DocumentChange::new(
            ChangeType::PageInsert,
            Some(inserted_page_number),
            format!("Insert page {} at position {}", inserted_page_number, self.position),
            self.previous_state.clone().unwrap(),
            new_state,
        );
        
        // Add user information if available
        let change = if let Some(ref user) = context.user_info {
            change.with_author(user.name.clone())
        } else {
            change
        };
        
        self.set_change(change.clone());
        context.record_change(change);
        
        self.base.set_executed(true);
        Ok(())
    }
    
    fn undo(&mut self, context: &mut ExecutionContext) -> CommandResult<()> {
        if !self.base.is_executed() {
            return Err(CommandError::UndoRedoFailed(
                "Command has not been executed".to_string()
            ));
        }
        
        let inserted_page_number = self.inserted_page_number
            .ok_or_else(|| CommandError::UndoRedoFailed(
                "No inserted page number available for undo".to_string()
            ))?;
        
        // Remove the inserted page
        context.document.remove_page(inserted_page_number)
            .map_err(|e| CommandError::UndoRedoFailed(e.to_string()))?;
        
        self.base.set_executed(false);
        Ok(())
    }
    
    fn redo(&mut self, context: &mut ExecutionContext) -> CommandResult<()> {
        if self.base.is_executed() {
            return Err(CommandError::UndoRedoFailed(
                "Command is already executed".to_string()
            ));
        }
        
        // Re-execute the command
        self.execute(context)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::document::{EditableDocument, EditingConfig};
    use crate::types::DocumentInfo;
    
    fn create_test_document() -> EditableDocument {
        let doc_info = DocumentInfo::new(
            2, // Start with 2 pages
            Some("Test Document".to_string()),
            None, None, None, None, None, None,
            false,
            crate::types::DocumentPermissions {
                can_print: true,
                can_modify: true,
                can_copy: true,
                can_add_notes: true,
                can_fill_forms: true,
                can_extract_for_accessibility: true,
                can_assemble: true,
                can_print_high_quality: true,
            }
        );
        
        EditableDocument::new(
            doc_info,
            vec![],
            EditingConfig::default(),
        ).unwrap()
    }
    
    #[test]
    fn test_insert_page_command_creation() {
        let command = InsertPageCommand::new(1, None);
        
        assert_eq!(command.position(), 1);
        assert!(command.settings().is_none());
        assert!(command.inserted_page_number().is_none());
    }
    
    #[test]
    fn test_insert_page_command_validation() {
        let document = create_test_document();
        let context = ExecutionContext::new(document);
        
        let command = InsertPageCommand::new(1, None);
        let validation = command.validate(&context);
        assert!(validation.is_valid);
    }
    
    #[test]
    fn test_insert_page_command_with_settings() {
        let settings = PageSettings {
            width: 800.0,
            height: 600.0,
            ..PageSettings::default()
        };
        
        let command = InsertPageCommand::new(1, None).with_settings(settings.clone());
        
        assert_eq!(command.settings().unwrap().width, 800.0);
        assert_eq!(command.settings().unwrap().height, 600.0);
    }
}
