/// Page management commands
/// 
/// This module provides commands for page operations including
/// insertion, deletion, movement, and duplication.

pub mod insert_page;
pub mod delete_page;
pub mod move_page;
pub mod duplicate_page;

pub use insert_page::InsertPageCommand;
pub use delete_page::DeletePageCommand;
pub use move_page::MovePageCommand;
pub use duplicate_page::DuplicatePageCommand;

use super::*;
use crate::document::{EditableDocument, PageSettings, DocumentChange, ChangeType, ChangeState};

/// Page command utilities
pub mod utils {
    use super::*;
    
    /// Validate page insertion position
    pub fn validate_page_insertion_position(
        document: &EditableDocument,
        position: u32,
    ) -> CommandResult<()> {
        // Position can be 0 (insert at beginning), 1 to page_count (insert after page), 
        // or page_count + 1 (insert at end)
        if position > document.page_count() + 1 {
            return Err(CommandError::InvalidParameters(
                format!("Invalid insertion position: {} (document has {} pages)", 
                        position, document.page_count())
            ));
        }
        Ok(())
    }
    
    /// Create a page change state
    pub fn create_page_change_state(
        page_number: u32,
        settings: PageSettings,
        exists: bool,
    ) -> ChangeState {
        ChangeState::Page {
            page_number,
            settings,
            exists,
        }
    }
    
    /// Get default page settings from document
    pub fn get_default_page_settings(document: &EditableDocument) -> PageSettings {
        // In a real implementation, this would get settings from the document's config
        // For now, return default settings
        PageSettings::default()
    }
    
    /// Validate page move operation
    pub fn validate_page_move(
        document: &EditableDocument,
        from_page: u32,
        to_position: u32,
    ) -> CommandResult<()> {
        // Validate source page
        if let Err(e) = crate::commands::utils::validate_page_number(from_page, document) {
            return Err(e);
        }
        
        // Validate destination position
        if to_position == 0 || to_position > document.page_count() {
            return Err(CommandError::InvalidParameters(
                format!("Invalid destination position: {} (document has {} pages)", 
                        to_position, document.page_count())
            ));
        }
        
        // Check if move is necessary
        if from_page == to_position {
            return Err(CommandError::InvalidParameters(
                "Source and destination positions are the same".to_string()
            ));
        }
        
        Ok(())
    }
    
    /// Check if page can be deleted
    pub fn can_delete_page(document: &EditableDocument, page: u32) -> CommandResult<()> {
        // Validate page number
        if let Err(e) = crate::commands::utils::validate_page_number(page, document) {
            return Err(e);
        }
        
        // Check if it's the last page
        if document.page_count() == 1 {
            return Err(CommandError::InvalidParameters(
                "Cannot delete the last page in the document".to_string()
            ));
        }
        
        Ok(())
    }
    
    /// Get page information for change tracking
    pub fn get_page_info(
        document: &EditableDocument,
        page: u32,
    ) -> CommandResult<(PageSettings, bool)> {
        match document.get_page(page) {
            Ok(page_obj) => Ok((page_obj.settings().clone(), true)),
            Err(_) => Ok((PageSettings::default(), false)),
        }
    }
    
    /// Calculate new page numbers after insertion
    pub fn calculate_page_numbers_after_insertion(
        document: &EditableDocument,
        insert_position: u32,
    ) -> Vec<(u32, u32)> {
        let mut changes = Vec::new();
        
        // Pages after the insertion point need to be renumbered
        for page_num in insert_position..=document.page_count() {
            changes.push((page_num, page_num + 1));
        }
        
        changes
    }
    
    /// Calculate new page numbers after deletion
    pub fn calculate_page_numbers_after_deletion(
        document: &EditableDocument,
        delete_page: u32,
    ) -> Vec<(u32, u32)> {
        let mut changes = Vec::new();
        
        // Pages after the deleted page need to be renumbered
        for page_num in (delete_page + 1)..=document.page_count() {
            changes.push((page_num, page_num - 1));
        }
        
        changes
    }
    
    /// Calculate new page numbers after move
    pub fn calculate_page_numbers_after_move(
        document: &EditableDocument,
        from_page: u32,
        to_position: u32,
    ) -> Vec<(u32, u32)> {
        let mut changes = Vec::new();
        
        if from_page < to_position {
            // Moving forward: pages between from and to shift backward
            for page_num in (from_page + 1)..=to_position {
                changes.push((page_num, page_num - 1));
            }
            changes.push((from_page, to_position));
        } else {
            // Moving backward: pages between to and from shift forward
            for page_num in to_position..from_page {
                changes.push((page_num, page_num + 1));
            }
            changes.push((from_page, to_position));
        }
        
        changes
    }
    
    /// Validate page duplication
    pub fn validate_page_duplication(
        document: &EditableDocument,
        source_page: u32,
        insert_after: bool,
    ) -> CommandResult<u32> {
        // Validate source page
        if let Err(e) = crate::commands::utils::validate_page_number(source_page, document) {
            return Err(e);
        }
        
        // Calculate insertion position
        let insert_position = if insert_after {
            source_page + 1
        } else {
            source_page
        };
        
        // Validate insertion position
        validate_page_insertion_position(document, insert_position)?;
        
        Ok(insert_position)
    }
}
