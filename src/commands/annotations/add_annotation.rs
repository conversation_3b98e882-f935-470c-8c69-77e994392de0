/// Add annotation command implementation
/// 
/// This command handles adding annotations to PDF pages.

use super::*;
use crate::commands::{Command, CommandMetadata, CommandResult, CommandError, ExecutionContext, ValidationResult};
use crate::document::{DocumentChange, ChangeType, ChangeState};
use crate::types::{Point, Rect};
use crate::impl_command_base;

use std::any::Any;

/// Annotation type enumeration
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum AnnotationType {
    Text,
    Highlight,
    Note,
    Link,
    Stamp,
    FreeText,
    Ink,
    Square,
    Circle,
}

/// Annotation properties
#[derive(Debug, Clone)]
pub struct AnnotationProperties {
    /// Annotation content/text
    pub content: String,
    /// Author name
    pub author: Option<String>,
    /// Annotation color
    pub color: crate::document::Color,
    /// Opacity (0.0 to 1.0)
    pub opacity: f32,
    /// Whether annotation is open/visible
    pub open: bool,
    /// Custom properties
    pub custom_properties: std::collections::HashMap<String, String>,
}

impl Default for AnnotationProperties {
    fn default() -> Self {
        Self {
            content: String::new(),
            author: None,
            color: crate::document::Color::yellow(),
            opacity: 1.0,
            open: false,
            custom_properties: std::collections::HashMap::new(),
        }
    }
}

/// Command for adding annotations
#[derive(Debug, Clone)]
pub struct AddAnnotationCommand {
    base: crate::commands::base::BaseCommand,
    page: u32,
    annotation_type: AnnotationType,
    bounds: Rect,
    properties: AnnotationProperties,
    added_annotation_id: Option<String>,
    previous_state: Option<ChangeState>,
}

impl AddAnnotationCommand {
    /// Create a new add annotation command
    pub fn new(
        page: u32,
        annotation_type: AnnotationType,
        bounds: Rect,
        properties: AnnotationProperties,
    ) -> Self {
        let metadata = CommandMetadata::new(
            "AddAnnotation".to_string(),
            format!("Add {:?} annotation on page {} at ({}, {})", 
                    annotation_type, page, bounds.x, bounds.y)
        );
        
        Self {
            base: crate::commands::base::BaseCommand::new(metadata),
            page,
            annotation_type,
            bounds,
            properties,
            added_annotation_id: None,
            previous_state: None,
        }
    }
    
    /// Create a text annotation
    pub fn text_annotation(page: u32, position: Point, content: String) -> Self {
        let bounds = Rect::new(position.x, position.y, 20.0, 20.0); // Default size
        let mut properties = AnnotationProperties::default();
        properties.content = content;
        
        Self::new(page, AnnotationType::Text, bounds, properties)
    }
    
    /// Create a highlight annotation
    pub fn highlight_annotation(page: u32, bounds: Rect) -> Self {
        let mut properties = AnnotationProperties::default();
        properties.color = crate::document::Color::rgb(1.0, 1.0, 0.0); // Yellow
        properties.opacity = 0.5;
        
        Self::new(page, AnnotationType::Highlight, bounds, properties)
    }
    
    /// Create a note annotation
    pub fn note_annotation(page: u32, position: Point, content: String) -> Self {
        let bounds = Rect::new(position.x, position.y, 18.0, 18.0); // Standard note size
        let mut properties = AnnotationProperties::default();
        properties.content = content;
        properties.open = true;
        
        Self::new(page, AnnotationType::Note, bounds, properties)
    }
    
    /// Set annotation author
    pub fn with_author(mut self, author: String) -> Self {
        self.properties.author = Some(author);
        self
    }
    
    /// Set annotation color
    pub fn with_color(mut self, color: crate::document::Color) -> Self {
        self.properties.color = color;
        self
    }
    
    /// Set annotation opacity
    pub fn with_opacity(mut self, opacity: f32) -> Self {
        self.properties.opacity = opacity.clamp(0.0, 1.0);
        self
    }
    
    /// Get the page number
    pub fn page(&self) -> u32 {
        self.page
    }
    
    /// Get the annotation type
    pub fn annotation_type(&self) -> AnnotationType {
        self.annotation_type
    }
    
    /// Get the annotation bounds
    pub fn bounds(&self) -> Rect {
        self.bounds
    }
    
    /// Get the added annotation ID (available after execution)
    pub fn added_annotation_id(&self) -> Option<&str> {
        self.added_annotation_id.as_deref()
    }
}

impl_command_base!(AddAnnotationCommand);

impl Command for AddAnnotationCommand {
    fn validate(&self, context: &ExecutionContext) -> ValidationResult {
        let mut result = ValidationResult::valid();
        
        // Validate page exists
        if self.page == 0 || self.page > context.document.page_count() {
            result.add_error(format!("Invalid page number: {}", self.page));
        }
        
        // Validate bounds
        if self.bounds.width <= 0.0 || self.bounds.height <= 0.0 {
            result.add_error("Annotation bounds must have positive width and height".to_string());
        }
        
        // Validate opacity
        if self.properties.opacity < 0.0 || self.properties.opacity > 1.0 {
            result.add_error("Opacity must be between 0.0 and 1.0".to_string());
        }
        
        // Validate content for text-based annotations
        match self.annotation_type {
            AnnotationType::Text | AnnotationType::Note | AnnotationType::FreeText => {
                if self.properties.content.is_empty() {
                    result.add_warning("Text annotation has no content".to_string());
                }
            }
            _ => {}
        }
        
        // Check permissions
        if let Err(e) = crate::commands::utils::check_permission(&context.document, "add_annotations") {
            result.add_error(e.to_string());
        }
        
        result
    }
    
    fn execute(&mut self, context: &mut ExecutionContext) -> CommandResult<()> {
        // Generate unique annotation ID
        let annotation_id = format!("annotation_{}", generate_unique_id());
        
        // Store previous state for undo
        self.previous_state = Some(create_annotation_change_state(
            self.page,
            &annotation_id,
            self.annotation_type,
            self.bounds,
            false, // Annotation doesn't exist yet
        ));
        
        // Add annotation to page
        let page = context.document.get_page_mut(self.page)
            .map_err(|e| CommandError::ExecutionFailed(e.to_string()))?;
        
        add_annotation_to_page(page, &annotation_id, self.annotation_type, self.bounds, &self.properties)?;
        
        self.added_annotation_id = Some(annotation_id.clone());
        
        // Create and record the document change
        let new_state = create_annotation_change_state(
            self.page,
            &annotation_id,
            self.annotation_type,
            self.bounds,
            true, // Annotation now exists
        );
        
        let change = DocumentChange::new(
            ChangeType::AnnotationAdd,
            Some(self.page),
            format!("Add {:?} annotation: {}", self.annotation_type, 
                    self.properties.content.chars().take(50).collect::<String>()),
            self.previous_state.clone().unwrap(),
            new_state,
        );
        
        // Add user information if available
        let change = if let Some(ref user) = context.user_info {
            change.with_author(user.name.clone())
        } else if let Some(ref author) = self.properties.author {
            change.with_author(author.clone())
        } else {
            change
        };
        
        self.set_change(change.clone());
        context.record_change(change);
        
        self.base.set_executed(true);
        Ok(())
    }
    
    fn undo(&mut self, context: &mut ExecutionContext) -> CommandResult<()> {
        if !self.base.is_executed() {
            return Err(CommandError::UndoRedoFailed(
                "Command has not been executed".to_string()
            ));
        }
        
        let annotation_id = self.added_annotation_id.as_ref()
            .ok_or_else(|| CommandError::UndoRedoFailed(
                "No added annotation ID available for undo".to_string()
            ))?;
        
        // Remove annotation from page
        let page = context.document.get_page_mut(self.page)
            .map_err(|e| CommandError::UndoRedoFailed(e.to_string()))?;
        
        remove_annotation_from_page(page, annotation_id)?;
        
        self.base.set_executed(false);
        Ok(())
    }
    
    fn redo(&mut self, context: &mut ExecutionContext) -> CommandResult<()> {
        if self.base.is_executed() {
            return Err(CommandError::UndoRedoFailed(
                "Command is already executed".to_string()
            ));
        }
        
        // Re-execute the command
        self.execute(context)
    }
}

// Helper functions

fn generate_unique_id() -> u64 {
    use std::collections::hash_map::DefaultHasher;
    use std::hash::{Hash, Hasher};
    use std::time::{SystemTime, UNIX_EPOCH};
    
    let mut hasher = DefaultHasher::new();
    SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_nanos().hash(&mut hasher);
    hasher.finish()
}

fn create_annotation_change_state(
    page: u32,
    annotation_id: &str,
    annotation_type: AnnotationType,
    bounds: Rect,
    exists: bool,
) -> ChangeState {
    ChangeState::Annotation {
        annotation_id: annotation_id.to_string(),
        annotation_type: crate::document::editable_page::AnnotationType::Text, // Convert
        bounds,
        content: String::new(),
        exists,
    }
}

fn add_annotation_to_page(
    page: &mut crate::document::EditablePage,
    annotation_id: &str,
    annotation_type: AnnotationType,
    bounds: Rect,
    properties: &AnnotationProperties,
) -> CommandResult<()> {
    // In a real implementation, this would:
    // 1. Create an annotation object with the specified properties
    // 2. Add it to the page's annotation layer
    // 3. Update the page layout and rendering
    
    Ok(())
}

fn remove_annotation_from_page(
    page: &mut crate::document::EditablePage,
    annotation_id: &str,
) -> CommandResult<()> {
    // In a real implementation, this would:
    // 1. Find the annotation object by ID
    // 2. Remove it from the page's annotation layer
    // 3. Update the page layout
    
    Ok(())
}
