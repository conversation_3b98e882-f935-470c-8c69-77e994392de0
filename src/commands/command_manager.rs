/// Command manager for executing, batching, and managing commands
/// 
/// This module provides the central command management system that
/// handles command execution, undo/redo operations, and batching.

use super::*;
use crate::document::{EditableDocument, ChangeTracker};
use crate::error::PDFResult;

use std::collections::VecDeque;
use std::sync::{Arc, Mutex};

/// Central command manager
#[derive(Debug)]
pub struct CommandManager {
    /// Execution context
    context: ExecutionContext,
    /// Command execution queue
    command_queue: VecDeque<Box<dyn Command>>,
    /// Currently executing batch
    current_batch: Option<CommandBatch>,
    /// Execution statistics
    stats: ExecutionStats,
    /// Whether the manager is processing commands
    processing: bool,
    /// Command execution listeners
    listeners: Vec<Box<dyn CommandListener>>,
}

impl CommandManager {
    /// Create a new command manager
    pub fn new(document: EditableDocument) -> Self {
        Self {
            context: ExecutionContext::new(document),
            command_queue: VecDeque::new(),
            current_batch: None,
            stats: ExecutionStats::default(),
            processing: false,
            listeners: Vec::new(),
        }
    }
    
    /// Execute a command immediately
    pub fn execute(&mut self, mut command: Box<dyn Command>) -> CommandResult<()> {
        let start_time = js_sys::Date::now() as u64;
        
        // Validate command
        let validation = command.validate(&self.context);
        if !validation.is_valid {
            return Err(CommandError::ValidationFailed(
                validation.errors.join("; ")
            ));
        }
        
        // Notify listeners
        self.notify_listeners(CommandEvent::BeforeExecute {
            command_name: command.metadata().name.clone(),
        });
        
        // Execute command
        let result = command.execute(&mut self.context);
        let duration = js_sys::Date::now() as u64 - start_time;
        
        // Record statistics
        self.stats.record_execution(duration, result.is_ok());
        
        // Handle result
        match result {
            Ok(()) => {
                // Add to current batch or execute immediately
                if let Some(ref mut batch) = self.current_batch {
                    batch.add_command(command);
                } else {
                    // Record change if command is undoable
                    if command.metadata().undoable {
                        if let Some(change) = command.get_change() {
                            self.context.record_change(change.clone());
                        }
                    }
                }
                
                self.notify_listeners(CommandEvent::AfterExecute {
                    command_name: command.metadata().name.clone(),
                    success: true,
                    duration,
                });
                
                Ok(())
            },
            Err(err) => {
                self.notify_listeners(CommandEvent::AfterExecute {
                    command_name: command.metadata().name.clone(),
                    success: false,
                    duration,
                });
                
                Err(err)
            }
        }
    }
    
    /// Queue a command for batch execution
    pub fn queue(&mut self, command: Box<dyn Command>) {
        self.command_queue.push_back(command);
    }
    
    /// Process all queued commands
    pub fn process_queue(&mut self) -> CommandResult<Vec<String>> {
        if self.processing {
            return Err(CommandError::ExecutionFailed(
                "Already processing commands".to_string()
            ));
        }
        
        self.processing = true;
        let mut executed_commands = Vec::new();
        let mut errors = Vec::new();
        
        while let Some(command) = self.command_queue.pop_front() {
            match self.execute(command) {
                Ok(()) => {
                    executed_commands.push("Command executed successfully".to_string());
                },
                Err(err) => {
                    errors.push(err.to_string());
                }
            }
        }
        
        self.processing = false;
        
        if errors.is_empty() {
            Ok(executed_commands)
        } else {
            Err(CommandError::ExecutionFailed(
                format!("Some commands failed: {}", errors.join("; "))
            ))
        }
    }
    
    /// Start a command batch
    pub fn start_batch(&mut self, options: BatchOptions) -> CommandResult<()> {
        if self.current_batch.is_some() {
            return Err(CommandError::ExecutionFailed(
                "Batch already in progress".to_string()
            ));
        }
        
        self.current_batch = Some(CommandBatch::new(options));
        Ok(())
    }
    
    /// End the current command batch
    pub fn end_batch(&mut self) -> CommandResult<()> {
        if let Some(batch) = self.current_batch.take() {
            // Execute the batch as a single composite command
            let composite = batch.into_composite_command();
            self.execute(Box::new(composite))?;
            Ok(())
        } else {
            Err(CommandError::ExecutionFailed(
                "No batch in progress".to_string()
            ))
        }
    }
    
    /// Undo the last command
    pub fn undo(&mut self) -> CommandResult<()> {
        if let Some(change) = self.context.document.change_tracker_mut().undo() {
            self.stats.record_undo();
            
            self.notify_listeners(CommandEvent::Undo {
                change_description: change.description.clone(),
            });
            
            Ok(())
        } else {
            Err(CommandError::UndoRedoFailed(
                "No commands to undo".to_string()
            ))
        }
    }
    
    /// Redo the last undone command
    pub fn redo(&mut self) -> CommandResult<()> {
        if let Some(change) = self.context.document.change_tracker_mut().redo() {
            self.stats.record_redo();
            
            self.notify_listeners(CommandEvent::Redo {
                change_description: change.description.clone(),
            });
            
            Ok(())
        } else {
            Err(CommandError::UndoRedoFailed(
                "No commands to redo".to_string()
            ))
        }
    }
    
    /// Check if undo is possible
    pub fn can_undo(&self) -> bool {
        self.context.document.change_tracker().can_undo()
    }
    
    /// Check if redo is possible
    pub fn can_redo(&self) -> bool {
        self.context.document.change_tracker().can_redo()
    }
    
    /// Get execution statistics
    pub fn stats(&self) -> &ExecutionStats {
        &self.stats
    }
    
    /// Get the document
    pub fn document(&self) -> &EditableDocument {
        &self.context.document
    }
    
    /// Get mutable document
    pub fn document_mut(&mut self) -> &mut EditableDocument {
        &mut self.context.document
    }
    
    /// Add a command listener
    pub fn add_listener(&mut self, listener: Box<dyn CommandListener>) {
        self.listeners.push(listener);
    }
    
    /// Notify all listeners of an event
    fn notify_listeners(&mut self, event: CommandEvent) {
        for listener in &mut self.listeners {
            listener.on_command_event(&event);
        }
    }
    
    /// Clear all queued commands
    pub fn clear_queue(&mut self) {
        self.command_queue.clear();
    }
    
    /// Get the number of queued commands
    pub fn queue_size(&self) -> usize {
        self.command_queue.len()
    }
    
    /// Check if a batch is in progress
    pub fn is_batch_in_progress(&self) -> bool {
        self.current_batch.is_some()
    }
}

/// Command batch for grouping related commands
#[derive(Debug)]
pub struct CommandBatch {
    /// Batch options
    options: BatchOptions,
    /// Commands in the batch
    commands: Vec<Box<dyn Command>>,
    /// Batch start time
    start_time: u64,
}

impl CommandBatch {
    pub fn new(options: BatchOptions) -> Self {
        Self {
            options,
            commands: Vec::new(),
            start_time: js_sys::Date::now() as u64,
        }
    }
    
    /// Add a command to the batch
    pub fn add_command(&mut self, command: Box<dyn Command>) {
        self.commands.push(command);
    }
    
    /// Get the number of commands in the batch
    pub fn command_count(&self) -> usize {
        self.commands.len()
    }
    
    /// Convert the batch into a composite command
    pub fn into_composite_command(self) -> CompositeCommand {
        let mut composite = CompositeCommand::new(
            self.options.name.clone(),
            self.options.description.clone(),
        );
        
        for command in self.commands {
            composite.add_command(command);
        }
        
        composite
    }
}

/// Batch execution options
#[derive(Debug, Clone)]
pub struct BatchOptions {
    /// Batch name
    pub name: String,
    /// Batch description
    pub description: String,
    /// Whether to stop on first error
    pub stop_on_error: bool,
    /// Whether to validate all commands before execution
    pub validate_before_execution: bool,
    /// Maximum execution time in milliseconds
    pub max_execution_time: Option<u64>,
}

impl BatchOptions {
    pub fn new(name: String, description: String) -> Self {
        Self {
            name,
            description,
            stop_on_error: true,
            validate_before_execution: true,
            max_execution_time: None,
        }
    }
    
    pub fn continue_on_error(mut self) -> Self {
        self.stop_on_error = false;
        self
    }
    
    pub fn skip_validation(mut self) -> Self {
        self.validate_before_execution = false;
        self
    }
    
    pub fn with_timeout(mut self, timeout_ms: u64) -> Self {
        self.max_execution_time = Some(timeout_ms);
        self
    }
}

/// Command event for notifications
#[derive(Debug, Clone)]
pub enum CommandEvent {
    BeforeExecute {
        command_name: String,
    },
    AfterExecute {
        command_name: String,
        success: bool,
        duration: u64,
    },
    Undo {
        change_description: String,
    },
    Redo {
        change_description: String,
    },
    BatchStarted {
        batch_name: String,
    },
    BatchCompleted {
        batch_name: String,
        commands_executed: usize,
        success: bool,
    },
}

/// Command listener trait
pub trait CommandListener: Send + Sync {
    fn on_command_event(&mut self, event: &CommandEvent);
}

/// Simple logging command listener
#[derive(Debug)]
pub struct LoggingCommandListener;

impl CommandListener for LoggingCommandListener {
    fn on_command_event(&mut self, event: &CommandEvent) {
        match event {
            CommandEvent::BeforeExecute { command_name } => {
                web_sys::console::log_1(&format!("Executing command: {}", command_name).into());
            },
            CommandEvent::AfterExecute { command_name, success, duration } => {
                web_sys::console::log_1(&format!(
                    "Command {} {} in {}ms", 
                    command_name, 
                    if *success { "succeeded" } else { "failed" },
                    duration
                ).into());
            },
            CommandEvent::Undo { change_description } => {
                web_sys::console::log_1(&format!("Undoing: {}", change_description).into());
            },
            CommandEvent::Redo { change_description } => {
                web_sys::console::log_1(&format!("Redoing: {}", change_description).into());
            },
            CommandEvent::BatchStarted { batch_name } => {
                web_sys::console::log_1(&format!("Starting batch: {}", batch_name).into());
            },
            CommandEvent::BatchCompleted { batch_name, commands_executed, success } => {
                web_sys::console::log_1(&format!(
                    "Batch {} completed: {} commands, success: {}", 
                    batch_name, commands_executed, success
                ).into());
            },
        }
    }
}
