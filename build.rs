use std::env;
use std::path::PathBuf;

fn main() {
    // Tell cargo to rerun this build script if any of these files change
    println!("cargo:rerun-if-changed=build.rs");
    println!("cargo:rerun-if-changed=Cargo.toml");
    
    // Set up environment for WebAssembly build
    if env::var("CARGO_CFG_TARGET_ARCH").unwrap_or_default() == "wasm32" {
        // Enable WebAssembly-specific features
        println!("cargo:rustc-cfg=feature=\"wasm\"");

        // Set optimization flags for WebAssembly
        println!("cargo:rustc-env=RUSTFLAGS=-C target-feature=+simd128");
    }
    
    // Check for PDFium library
    if let Ok(pdfium_path) = env::var("PDFIUM_PATH") {
        println!("cargo:rustc-link-search=native={}", pdfium_path);
        println!("cargo:rustc-link-lib=pdfium");
    }
    
    // Set up include paths
    let out_dir = PathBuf::from(env::var("OUT_DIR").unwrap());
    println!("cargo:rustc-env=OUT_DIR={}", out_dir.display());
    
    // Generate version information
    let version = env::var("CARGO_PKG_VERSION").unwrap();
    println!("cargo:rustc-env=PDF_ENGINE_VERSION={}", version);
    
    // Check for debug build
    if env::var("PROFILE").unwrap_or_default() == "debug" {
        println!("cargo:rustc-cfg=debug_build");
    }

    // Check for editing features
    if cfg!(feature = "editing") {
        println!("cargo:rustc-cfg=editing_enabled");
        println!("cargo:warning=Building with PDF editing support");
    }

    // Check for specific editing features
    if cfg!(feature = "text-editing") {
        println!("cargo:rustc-cfg=text_editing_enabled");
    }

    if cfg!(feature = "page-editing") {
        println!("cargo:rustc-cfg=page_editing_enabled");
    }

    if cfg!(feature = "graphics-editing") {
        println!("cargo:rustc-cfg=graphics_editing_enabled");
    }

    if cfg!(feature = "annotations") {
        println!("cargo:rustc-cfg=annotations_enabled");
    }

    if cfg!(feature = "forms") {
        println!("cargo:rustc-cfg=forms_enabled");
    }

    // Check for threading support
    if cfg!(feature = "threading") {
        println!("cargo:rustc-cfg=threading_enabled");
    }

    // Check for SIMD support
    if cfg!(feature = "simd") {
        println!("cargo:rustc-cfg=simd_enabled");
    }
}
