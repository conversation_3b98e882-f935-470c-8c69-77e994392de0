# Cargo configuration for PDF Editor

[build]
# Default target for WebAssembly builds
target = "wasm32-unknown-unknown"

[target.wasm32-unknown-unknown]
# WebAssembly-specific configuration
rustflags = [
    "-C", "target-feature=+simd128",
    "-C", "target-feature=+bulk-memory",
    "-C", "target-feature=+mutable-globals",
]

# Linker configuration for WebAssembly
linker = "wasm-ld"

# Environment variables for development
[env]
# Enable debug logging in development
RUST_LOG = { value = "debug", condition = "cfg(debug_assertions)" }

# WebAssembly pack configuration
WASM_PACK_PROFILE = { value = "release", condition = "cfg(not(debug_assertions))" }

# Optimization settings for different profiles
[profile.dev]
# Development profile - faster compilation
opt-level = 0
debug = true
debug-assertions = true
overflow-checks = true
lto = false
panic = 'unwind'
incremental = true
codegen-units = 256

[profile.release]
# Release profile - optimized for size and speed
opt-level = 3
debug = false
debug-assertions = false
overflow-checks = false
lto = true
panic = 'abort'
incremental = false
codegen-units = 1

# WebAssembly-specific release profile
[profile.wasm-release]
inherits = "release"
opt-level = "s"  # Optimize for size
lto = true
panic = 'abort'

# Alias for common commands
[alias]
# Build for WebAssembly
wasm = "build --target wasm32-unknown-unknown"

# Build with editing features
edit = "build --features editing"

# Build with all editing features
edit-full = "build --features editing,text-editing,page-editing,graphics-editing,annotations,forms"

# Test with editing features
test-edit = "test --features editing"

# Check with all features
check-all = "check --all-features"
