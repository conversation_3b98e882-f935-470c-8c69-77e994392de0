[package]
name = "pdf-render-engine"
version = "0.1.0"
edition = "2021"
description = "High-performance PDF rendering engine with WebAssembly support"
license = "MIT"

[lib]
crate-type = ["cdylib", "rlib"]

[dependencies]
# WebAssembly bindings
wasm-bindgen = "0.2"
wasm-bindgen-futures = "0.4"
js-sys = "0.3"

# Web APIs
web-sys = { version = "0.3", features = [
  "console",
  "Document",
  "Element",
  "HtmlCanvasElement",
  "CanvasRenderingContext2d",
  "WebGlRenderingContext",
  "WebGl2RenderingContext",
  "ImageData",
  "Worker",
  "MessageEvent",
  "ErrorEvent",
  "Performance",
  "Window",
  "Blob",
  "HtmlElement",
] }



# PDF parsing libraries (hybrid approach)
# Primary: PDFium bindings for maximum compatibility
pdfium-render = { version = "0.8", features = ["sync", "thread_safe"], optional = true }

# Fallback: Pure Rust PDF libraries
lopdf = "0.32"
pdf = "0.8"

# Additional PDF processing
pdf-extract = "0.7"

# PDF Writing and Editing
pdf-writer = "0.9"

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
serde-wasm-bindgen = "0.6"

# Error handling
thiserror = "1.0"
anyhow = "1.0"

# Utilities
lru = "0.12"
once_cell = "1.19"
log = "0.4"

# Math and graphics
nalgebra = "0.32"
image = { version = "0.24", features = ["png", "jpeg"] }

# Text Processing
unicode-segmentation = "1.10"
unicode-width = "0.1"

# Date/Time for annotations and metadata
chrono = { version = "0.4", features = ["serde"] }

# Compression
flate2 = "1.0"

# Color management
palette = "0.7"

# Additional editing dependencies
uuid = { version = "1.0", features = ["v4", "serde"] }
base64 = "0.21"
sha2 = "0.10"

# WebAssembly utilities
wasm-logger = { version = "0.2", optional = true }
console_error_panic_hook = { version = "0.1", optional = true }

# Async runtime for WebAssembly
wasm-bindgen-rayon = { version = "1.0", optional = true }
rayon = { version = "1.7", optional = true }

[dependencies.getrandom]
version = "0.2"
features = ["js"]

[features]
default = ["lopdf-parser", "webgl-rendering", "editing"]

# Parser backends
pdfium-parser = ["pdfium-render"]
lopdf-parser = []
pdf-rs-parser = []

# Rendering backends
webgl-rendering = []
canvas2d-rendering = []

# Editing features
editing = []
text-editing = ["editing"]
page-editing = ["editing"]
graphics-editing = ["editing"]
annotations = ["editing"]
forms = ["editing"]

# Performance features
threading = ["wasm-bindgen-rayon", "rayon"]
simd = []

# Development features
debug-logging = ["wasm-logger", "console_error_panic_hook"]

[profile.release]
# Optimize for size and performance
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"

[profile.release.package."*"]
opt-level = 3

# WebAssembly specific optimizations
[package.metadata.wasm-pack.profile.release]
wasm-opt = ["-Oz", "--enable-mutable-globals"]
