cargo:rerun-if-changed=build.rs
cargo:rerun-if-changed=Cargo.toml
cargo:rustc-cfg=feature="wasm"
cargo:rustc-env=RUSTFLAGS=-C target-feature=+simd128
cargo:rustc-env=OUT_DIR=C:\Users\<USER>\Desktop\engine\target\wasm32-unknown-unknown\debug\build\pdf-render-engine-c5d80362e1249696\out
cargo:rustc-env=PDF_ENGINE_VERSION=0.1.0
cargo:rustc-cfg=debug_build
cargo:rustc-cfg=editing_enabled
cargo:warning=Building with PDF editing support
