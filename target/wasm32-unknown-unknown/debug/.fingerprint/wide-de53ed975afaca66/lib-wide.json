{"rustc": 7868289081541623310, "features": "[\"std\"]", "declared_features": "[\"default\", \"serde\", \"std\"]", "target": 10838888221915111951, "profile": 12366199790041765268, "path": 2133759931269942228, "deps": [[6643739152182419278, "bytemuck", false, 5473867846329522441], [6942256293210557013, "safe_arch", false, 10284098903647787171]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\debug\\.fingerprint\\wide-de53ed975afaca66\\dep-lib-wide", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 6466336266548934813, "compile_kind": 14682669768258224367}