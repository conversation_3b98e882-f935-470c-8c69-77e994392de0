{"rustc": 7868289081541623310, "features": "[]", "declared_features": "[]", "target": 1524667692659508025, "profile": 12366199790041765268, "path": 6833503033480144409, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\debug\\.fingerprint\\equivalent-865df265ba925e2e\\dep-lib-equivalent", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 6466336266548934813, "compile_kind": 14682669768258224367}