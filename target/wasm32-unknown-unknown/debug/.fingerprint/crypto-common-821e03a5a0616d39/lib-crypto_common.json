{"rustc": 7868289081541623310, "features": "[\"std\"]", "declared_features": "[\"getrandom\", \"rand_core\", \"std\"]", "target": 16242158919585437602, "profile": 12366199790041765268, "path": 7428880216396116378, "deps": [[10520923840501062997, "generic_array", false, 15551253550113265615], [17001665395952474378, "typenum", false, 8920551181043653859]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\debug\\.fingerprint\\crypto-common-821e03a5a0616d39\\dep-lib-crypto_common", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 6466336266548934813, "compile_kind": 14682669768258224367}