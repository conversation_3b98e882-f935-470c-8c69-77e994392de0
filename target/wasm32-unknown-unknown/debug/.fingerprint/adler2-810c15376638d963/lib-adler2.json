{"rustc": 7868289081541623310, "features": "[]", "declared_features": "[\"core\", \"default\", \"rustc-dep-of-std\", \"std\"]", "target": 6569825234462323107, "profile": 12366199790041765268, "path": 472059426708796855, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\debug\\.fingerprint\\adler2-810c15376638d963\\dep-lib-adler2", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 6466336266548934813, "compile_kind": 14682669768258224367}