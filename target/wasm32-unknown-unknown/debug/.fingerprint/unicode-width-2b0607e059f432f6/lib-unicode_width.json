{"rustc": 7868289081541623310, "features": "[\"cjk\", \"default\"]", "declared_features": "[\"cjk\", \"compiler_builtins\", \"core\", \"default\", \"no_std\", \"rustc-dep-of-std\", \"std\"]", "target": 16876147670056848225, "profile": 12366199790041765268, "path": 13176386860339235642, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\debug\\.fingerprint\\unicode-width-2b0607e059f432f6\\dep-lib-unicode_width", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 6466336266548934813, "compile_kind": 14682669768258224367}