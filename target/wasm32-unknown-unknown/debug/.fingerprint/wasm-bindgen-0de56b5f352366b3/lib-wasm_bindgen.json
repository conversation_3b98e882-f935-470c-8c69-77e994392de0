{"rustc": 7868289081541623310, "features": "[\"default\", \"msrv\", \"rustversion\", \"std\"]", "declared_features": "[\"default\", \"enable-interning\", \"gg-alloc\", \"msrv\", \"rustversion\", \"serde\", \"serde-serialize\", \"serde_json\", \"spans\", \"std\", \"strict-macro\", \"xxx_debug_only_print_generated_code\"]", "target": 4070942113156591848, "profile": 17565198560131644146, "path": 15176666018012730968, "deps": [[3722963349756955755, "once_cell", false, 6188948469859978489], [3894621559386490394, "wasm_bindgen_macro", false, 5207154722121840883], [7826122624549889939, "wasm_bindgen_shared", false, 7398754800026045403], [7843059260364151289, "cfg_if", false, 11441823997300870070], [14156967978702956262, "rustversion", false, 16713827135469811370], [17362525766049117937, "build_script_build", false, 15896757567113405039]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\debug\\.fingerprint\\wasm-bindgen-0de56b5f352366b3\\dep-lib-wasm_bindgen", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 6466336266548934813, "compile_kind": 14682669768258224367}