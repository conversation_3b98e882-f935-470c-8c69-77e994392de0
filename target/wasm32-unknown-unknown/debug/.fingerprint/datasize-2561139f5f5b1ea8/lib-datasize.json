{"rustc": 7868289081541623310, "features": "[\"const-generics\", \"default\", \"std\"]", "declared_features": "[\"const-generics\", \"default\", \"detailed\", \"fake_clock-types\", \"fake_instant\", \"futures\", \"futures-types\", \"serde\", \"smallvec\", \"smallvec-types\", \"std\", \"tokio\", \"tokio-types\"]", "target": 9547001476781516983, "profile": 12366199790041765268, "path": 13184497060210453584, "deps": [[11545065543132831861, "datasize_derive", false, 17670996711609162241]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\debug\\.fingerprint\\datasize-2561139f5f5b1ea8\\dep-lib-datasize", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 6466336266548934813, "compile_kind": 14682669768258224367}