{"rustc": 7868289081541623310, "features": "[]", "declared_features": "[\"arbitrary\", \"bincode\", \"const_generics\", \"const_new\", \"debugger_visualizer\", \"drain_filter\", \"drain_keep_rest\", \"impl_bincode\", \"malloc_size_of\", \"may_dangle\", \"serde\", \"specialization\", \"union\", \"unty\", \"write\"]", "target": 9091769176333489034, "profile": 12366199790041765268, "path": 4435294667177284128, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\debug\\.fingerprint\\smallvec-520aac50d2e44c49\\dep-lib-smallvec", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 6466336266548934813, "compile_kind": 14682669768258224367}