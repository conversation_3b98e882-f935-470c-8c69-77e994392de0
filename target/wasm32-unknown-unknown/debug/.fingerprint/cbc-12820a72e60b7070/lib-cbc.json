{"rustc": 7868289081541623310, "features": "[\"block-padding\", \"default\"]", "declared_features": "[\"alloc\", \"block-padding\", \"default\", \"std\", \"zeroize\"]", "target": 5103841873489430697, "profile": 12366199790041765268, "path": 2309543417798998360, "deps": [[7916416211798676886, "cipher", false, 3910763925935598191]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\debug\\.fingerprint\\cbc-12820a72e60b7070\\dep-lib-cbc", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 6466336266548934813, "compile_kind": 14682669768258224367}