{"rustc": 7868289081541623310, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[17362525766049117937, "build_script_build", false, 9559731660508410], [7826122624549889939, "build_script_build", false, 5099425155420782086]], "local": [{"RerunIfChanged": {"output": "wasm32-unknown-unknown\\debug\\build\\wasm-bindgen-8fd145abd04ec571\\output", "paths": ["build.rs"]}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 0, "compile_kind": 0}