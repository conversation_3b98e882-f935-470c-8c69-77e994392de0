{"rustc": 7868289081541623310, "features": "[\"alloc\", \"default\", \"race\", \"std\"]", "declared_features": "[\"alloc\", \"atomic-polyfill\", \"critical-section\", \"default\", \"parking_lot\", \"portable-atomic\", \"race\", \"std\", \"unstable\"]", "target": 17524666916136250164, "profile": 12366199790041765268, "path": 545187386584023746, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\debug\\.fingerprint\\once_cell-6e950b4bfe3c96cc\\dep-lib-once_cell", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 6466336266548934813, "compile_kind": 14682669768258224367}