{"rustc": 7868289081541623310, "features": "[\"default\", \"js\", \"rng\", \"serde\", \"std\", \"v4\"]", "declared_features": "[\"arbitrary\", \"atomic\", \"borsh\", \"bytemuck\", \"default\", \"fast-rng\", \"js\", \"macro-diagnostics\", \"md5\", \"rng\", \"rng-getrandom\", \"rng-rand\", \"serde\", \"sha1\", \"slog\", \"std\", \"uuid-rng-internal-lib\", \"v1\", \"v3\", \"v4\", \"v5\", \"v6\", \"v7\", \"v8\", \"zerocopy\"]", "target": 10485754080552990909, "profile": 6192087377093851552, "path": 5968946726143630987, "deps": [[14851956875005785803, "serde", false, 2536648194185748128], [17362525766049117937, "wasm_bindgen", false, 1536282234363896142]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\debug\\.fingerprint\\uuid-8741a3ba40dab9b6\\dep-lib-uuid", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 6466336266548934813, "compile_kind": 14682669768258224367}