{"rustc": 7868289081541623310, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[14851956875005785803, "build_script_build", false, 9073555569605790674]], "local": [{"RerunIfChanged": {"output": "wasm32-unknown-unknown\\debug\\build\\serde-da3c4462029bbe48\\output", "paths": ["build.rs"]}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 0, "compile_kind": 0}