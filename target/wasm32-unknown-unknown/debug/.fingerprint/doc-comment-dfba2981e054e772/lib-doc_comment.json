{"rustc": 7868289081541623310, "features": "[]", "declared_features": "[\"no_core\", \"old_macros\"]", "target": 919102347318276249, "profile": 12366199790041765268, "path": 18212156474859360755, "deps": [[18000218614148971598, "build_script_build", false, 7694726198621671344]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\debug\\.fingerprint\\doc-comment-dfba2981e054e772\\dep-lib-doc_comment", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 6466336266548934813, "compile_kind": 14682669768258224367}