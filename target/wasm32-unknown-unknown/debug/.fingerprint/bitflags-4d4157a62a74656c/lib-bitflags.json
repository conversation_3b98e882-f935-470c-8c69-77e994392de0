{"rustc": 7868289081541623310, "features": "[\"default\"]", "declared_features": "[\"compiler_builtins\", \"core\", \"default\", \"example_generated\", \"rustc-dep-of-std\"]", "target": 12919857562465245259, "profile": 12366199790041765268, "path": 2283864703154431057, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\debug\\.fingerprint\\bitflags-4d4157a62a74656c\\dep-lib-bitflags", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 6466336266548934813, "compile_kind": 14682669768258224367}