{"rustc": 7868289081541623310, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[1852463361802237065, "build_script_build", false, 10314218010402425902]], "local": [{"RerunIfChanged": {"output": "wasm32-unknown-unknown\\debug\\build\\anyhow-b8a75041c5239974\\output", "paths": ["src/nightly.rs"]}}, {"RerunIfEnvChanged": {"var": "RUSTC_BOOTSTRAP", "val": null}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 0, "compile_kind": 0}