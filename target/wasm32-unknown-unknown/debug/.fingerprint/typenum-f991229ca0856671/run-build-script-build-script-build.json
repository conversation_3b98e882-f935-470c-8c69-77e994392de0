{"rustc": 7868289081541623310, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[17001665395952474378, "build_script_build", false, 8959907793459619397]], "local": [{"RerunIfChanged": {"output": "wasm32-unknown-unknown\\debug\\build\\typenum-f991229ca0856671\\output", "paths": ["tests"]}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 0, "compile_kind": 0}