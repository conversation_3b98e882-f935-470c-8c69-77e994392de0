{"rustc": 7868289081541623310, "features": "[\"default\", \"std\"]", "declared_features": "[\"default\", \"futures-core\", \"futures-core-03-stream\", \"std\"]", "target": 4429042720284741532, "profile": 17565198560131644146, "path": 14084935036843235177, "deps": [[1267401389414215502, "js_sys", false, 4612008468544780008], [3722963349756955755, "once_cell", false, 6188948469859978489], [7843059260364151289, "cfg_if", false, 11441823997300870070], [17362525766049117937, "wasm_bindgen", false, 1536282234363896142]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\debug\\.fingerprint\\wasm-bindgen-futures-f1aaca4c68ddab5d\\dep-lib-wasm_bindgen_futures", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 6466336266548934813, "compile_kind": 14682669768258224367}