{"rustc": 7868289081541623310, "features": "[\"bytemuck\", \"default\"]", "declared_features": "[\"bytemuck\", \"default\"]", "target": 9287881243760046938, "profile": 12366199790041765268, "path": 7649923654402666989, "deps": [[6643739152182419278, "bytemuck", false, 5473867846329522441]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\debug\\.fingerprint\\safe_arch-ecd97c3b7f53b095\\dep-lib-safe_arch", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 6466336266548934813, "compile_kind": 14682669768258224367}