{"rustc": 7868289081541623310, "features": "[\"default\", \"std\"]", "declared_features": "[\"default\", \"nightly\", \"std\"]", "target": 10823605331999153028, "profile": 12366199790041765268, "path": 15675808976598110984, "deps": [[7312356825837975969, "build_script_build", false, 7408411342610039593], [7843059260364151289, "cfg_if", false, 11441823997300870070]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\debug\\.fingerprint\\crc32fast-e031f7523fa46425\\dep-lib-crc32fast", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 6466336266548934813, "compile_kind": 14682669768258224367}