{"rustc": 7868289081541623310, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[15826188163127377936, "build_script_build", false, 1107425946536173083]], "local": [{"RerunIfChanged": {"output": "wasm32-unknown-unknown\\debug\\build\\matrixmultiply-e289fa74a933090e\\output", "paths": ["build.rs"]}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 0, "compile_kind": 0}