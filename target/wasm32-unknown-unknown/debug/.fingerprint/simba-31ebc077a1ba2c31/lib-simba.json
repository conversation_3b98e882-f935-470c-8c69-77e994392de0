{"rustc": 7868289081541623310, "features": "[\"std\", \"wide\"]", "declared_features": "[\"cordic\", \"cuda\", \"cuda_std\", \"cust_core\", \"decimal\", \"default\", \"fixed\", \"libm\", \"libm_force\", \"packed_simd\", \"partial_fixed_point_support\", \"rand\", \"rkyv\", \"rkyv-serialize\", \"serde\", \"serde_serialize\", \"std\", \"wide\"]", "target": 18314989904106682660, "profile": 12366199790041765268, "path": 13292842008380283976, "deps": [[5157631553186200874, "num_traits", false, 9785100474583394429], [11243818633362483251, "wide", false, 18230824630692943298], [12319020793864570031, "num_complex", false, 11073544243266765973], [15677050387741058262, "approx", false, 16426070772779142081], [17605717126308396068, "paste", false, 5155406820522046835]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\debug\\.fingerprint\\simba-31ebc077a1ba2c31\\dep-lib-simba", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 6466336266548934813, "compile_kind": 14682669768258224367}