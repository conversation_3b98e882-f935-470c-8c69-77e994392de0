{"rustc": 7868289081541623310, "features": "[]", "declared_features": "[\"debug\"]", "target": 1089503997142669903, "profile": 12366199790041765268, "path": 10352429709062503162, "deps": [[16896555084957406727, "fax_derive", false, 5738934681047918482]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\debug\\.fingerprint\\fax-8a44a88828ef76c0\\dep-lib-fax", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 6466336266548934813, "compile_kind": 14682669768258224367}