{"rustc": 7868289081541623310, "features": "[\"const-generics\", \"default\", \"std\"]", "declared_features": "[\"const-generics\", \"default\", \"nightly\", \"std\"]", "target": 11204583500128257727, "profile": 12366199790041765268, "path": 2695306349682663892, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\debug\\.fingerprint\\simd-adler32-8cebb6aef857e208\\dep-lib-simd_adler32", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 6466336266548934813, "compile_kind": 14682669768258224367}