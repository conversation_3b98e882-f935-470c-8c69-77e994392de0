{"rustc": 7868289081541623310, "features": "[]", "declared_features": "[]", "target": 18192794659233165256, "profile": 12366199790041765268, "path": 6037275375070737208, "deps": [[1267401389414215502, "js_sys", false, 4612008468544780008], [14851956875005785803, "serde", false, 2536648194185748128], [17362525766049117937, "wasm_bindgen", false, 1536282234363896142]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\debug\\.fingerprint\\serde-wasm-bindgen-6cc6831cce3da871\\dep-lib-serde_wasm_bindgen", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 6466336266548934813, "compile_kind": 14682669768258224367}