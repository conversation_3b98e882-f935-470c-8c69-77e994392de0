{"rustc": 7868289081541623310, "features": "[\"default\", \"use_alloc\", \"use_std\"]", "declared_features": "[\"default\", \"use_alloc\", \"use_std\"]", "target": 9541170365560449339, "profile": 12366199790041765268, "path": 11081090816776157875, "deps": [[12170264697963848012, "either", false, 6354482192994236193]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\debug\\.fingerprint\\itertools-a0d1c057e6efc8c4\\dep-lib-itertools", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 6466336266548934813, "compile_kind": 14682669768258224367}