{"rustc": 7868289081541623310, "features": "[\"std\", \"use_std\"]", "declared_features": "[\"default\", \"serde\", \"std\", \"use_std\"]", "target": 17124342308084364240, "profile": 12366199790041765268, "path": 2074185452722199423, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\debug\\.fingerprint\\either-cd4de83de36df587\\dep-lib-either", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 6466336266548934813, "compile_kind": 14682669768258224367}