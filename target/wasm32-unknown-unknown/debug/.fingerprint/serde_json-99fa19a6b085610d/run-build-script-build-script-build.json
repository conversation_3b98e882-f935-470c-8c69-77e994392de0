{"rustc": 7868289081541623310, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[12832915883349295919, "build_script_build", false, 11264396523223780580]], "local": [{"RerunIfChanged": {"output": "wasm32-unknown-unknown\\debug\\build\\serde_json-99fa19a6b085610d\\output", "paths": ["build.rs"]}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 0, "compile_kind": 0}