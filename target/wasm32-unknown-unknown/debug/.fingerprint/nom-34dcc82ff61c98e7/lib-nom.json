{"rustc": 7868289081541623310, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"docsrs\", \"std\"]", "target": 15126381483855761411, "profile": 12366199790041765268, "path": 4780277962593068618, "deps": [[4917998273308230437, "minimal_lexical", false, 3472326260375997494], [15932120279885307830, "memchr", false, 15538015538257423020]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\debug\\.fingerprint\\nom-34dcc82ff61c98e7\\dep-lib-nom", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 6466336266548934813, "compile_kind": 14682669768258224367}