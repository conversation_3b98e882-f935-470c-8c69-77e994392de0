{"rustc": 7868289081541623310, "features": "[\"default\", \"simd\", \"simd-adler32\", \"with-alloc\"]", "declared_features": "[\"alloc\", \"block-boundary\", \"core\", \"default\", \"rustc-dep-of-std\", \"serde\", \"simd\", \"simd-adler32\", \"std\", \"with-alloc\"]", "target": 8661567070972402511, "profile": 3485444515778192238, "path": 9124921947395243039, "deps": [[4018467389006652250, "simd_adler32", false, 17552133868866341548], [7911289239703230891, "adler2", false, 8263500643727603042]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\debug\\.fingerprint\\miniz_oxide-67695a5fec643d5b\\dep-lib-miniz_oxide", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 6466336266548934813, "compile_kind": 14682669768258224367}