{"rustc": 7868289081541623310, "features": "[]", "declared_features": "[]", "target": 17187170812452057412, "profile": 8538602630216586199, "path": 1538488761278623810, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\debug\\.fingerprint\\num-conv-8f749ca96ec8d82f\\dep-lib-num_conv", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 6466336266548934813, "compile_kind": 14682669768258224367}