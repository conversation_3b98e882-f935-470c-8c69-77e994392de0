{"rustc": 7868289081541623310, "features": "[\"default\", \"std\"]", "declared_features": "[\"default\", \"loom\", \"nightly\", \"std\"]", "target": 9626079250877207070, "profile": 6912495516722188269, "path": 8486384840938824763, "deps": [[4468123440088164316, "build_script_build", false, 628258703520214916]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\debug\\.fingerprint\\crossbeam-utils-75499676a797f886\\dep-lib-crossbeam_utils", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 6466336266548934813, "compile_kind": 14682669768258224367}