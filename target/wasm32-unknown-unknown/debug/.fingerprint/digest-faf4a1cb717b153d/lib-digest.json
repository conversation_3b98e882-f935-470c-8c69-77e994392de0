{"rustc": 7868289081541623310, "features": "[\"alloc\", \"block-buffer\", \"core-api\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"blobby\", \"block-buffer\", \"const-oid\", \"core-api\", \"default\", \"dev\", \"mac\", \"oid\", \"rand_core\", \"std\", \"subtle\"]", "target": 7510122432137863311, "profile": 12366199790041765268, "path": 177554564215007717, "deps": [[2352660017780662552, "crypto_common", false, 14254780228989611061], [10626340395483396037, "block_buffer", false, 13142975770714694847]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\debug\\.fingerprint\\digest-faf4a1cb717b153d\\dep-lib-digest", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 6466336266548934813, "compile_kind": 14682669768258224367}