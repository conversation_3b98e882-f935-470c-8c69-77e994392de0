{"rustc": 7868289081541623310, "features": "[\"alloc\", \"default\", \"formatting\", \"parsing\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"formatting\", \"large-dates\", \"local-offset\", \"macros\", \"parsing\", \"quickcheck\", \"rand\", \"rand08\", \"rand09\", \"serde\", \"serde-human-readable\", \"serde-well-known\", \"std\", \"wasm-bindgen\"]", "target": 3713843603798095488, "profile": 16085371566749518016, "path": 3796163620750100707, "deps": [[678108754366636603, "time_core", false, 14069468722571932498], [724804171976944018, "num_conv", false, 4869887901105850601], [4731553544906053761, "deranged", false, 6598772155343014880], [5901133744777009488, "powerfmt", false, 13647536256979352355], [7695812897323945497, "itoa", false, 120816270137825543]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\debug\\.fingerprint\\time-795019f7d28b8a81\\dep-lib-time", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 6466336266548934813, "compile_kind": 14682669768258224367}