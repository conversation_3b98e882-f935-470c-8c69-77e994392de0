{"rustc": 7868289081541623310, "features": "[]", "declared_features": "[\"const-generics\", \"force_unix_path_separator\", \"i128\", \"no_std\", \"scale-info\", \"scale_info\", \"strict\"]", "target": 2349969882102649915, "profile": 12366199790041765268, "path": 7119190003201741369, "deps": [[17001665395952474378, "build_script_build", false, 1194032086972194237]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\debug\\.fingerprint\\typenum-a907dc76db50659d\\dep-lib-typenum", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 6466336266548934813, "compile_kind": 14682669768258224367}