{"rustc": 7868289081541623310, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[8008191657135824715, "build_script_build", false, 14680014555005790703]], "local": [{"RerunIfChanged": {"output": "wasm32-unknown-unknown\\debug\\build\\thiserror-3602c531ed39e7d7\\output", "paths": ["build/probe.rs"]}}, {"RerunIfEnvChanged": {"var": "RUSTC_BOOTSTRAP", "val": null}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 0, "compile_kind": 0}