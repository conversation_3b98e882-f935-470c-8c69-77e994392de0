{"rustc": 7868289081541623310, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[3746573929696391749, "build_script_build", false, 14214780550297126571]], "local": [{"RerunIfChanged": {"output": "wasm32-unknown-unknown\\debug\\build\\rayon-core-2c39f1a503da3acc\\output", "paths": ["build.rs"]}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 0, "compile_kind": 0}