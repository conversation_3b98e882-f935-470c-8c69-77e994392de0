{"rustc": 7868289081541623310, "features": "[\"default\", \"derive\", \"serde_derive\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"derive\", \"rc\", \"serde_derive\", \"std\", \"unstable\"]", "target": 11327258112168116673, "profile": 12366199790041765268, "path": 12909581454039846745, "deps": [[1511010206896078948, "serde_derive", false, 8572152775272130481], [2814771377364143997, "serde_core", false, 1700499807465575022], [14851956875005785803, "build_script_build", false, 2329188955672227139]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\debug\\.fingerprint\\serde-973e5e3a39ad7b13\\dep-lib-serde", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 6466336266548934813, "compile_kind": 14682669768258224367}