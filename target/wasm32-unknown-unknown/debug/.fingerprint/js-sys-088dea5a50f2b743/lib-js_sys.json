{"rustc": 7868289081541623310, "features": "[\"default\", \"std\"]", "declared_features": "[\"default\", \"std\"]", "target": 4913466754190795764, "profile": 4889702205571749717, "path": 5240653156372497212, "deps": [[3722963349756955755, "once_cell", false, 6188948469859978489], [17362525766049117937, "wasm_bindgen", false, 1536282234363896142]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\debug\\.fingerprint\\js-sys-088dea5a50f2b743\\dep-lib-js_sys", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 6466336266548934813, "compile_kind": 14682669768258224367}