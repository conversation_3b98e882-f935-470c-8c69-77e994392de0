{"rustc": 7868289081541623310, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"core\", \"default\", \"libc\", \"logging\", \"rustc-dep-of-std\", \"std\", \"use_std\"]", "target": 11745930252914242013, "profile": 12366199790041765268, "path": 7326161735121813010, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\debug\\.fingerprint\\memchr-af761ad9bc030b87\\dep-lib-memchr", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 6466336266548934813, "compile_kind": 14682669768258224367}