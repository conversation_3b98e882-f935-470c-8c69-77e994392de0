{"rustc": 7868289081541623310, "features": "[]", "declared_features": "[\"no_std\"]", "target": 14369684853076716314, "profile": 12366199790041765268, "path": 1129148187496791949, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\debug\\.fingerprint\\unicode-segmentation-f9d85c1403767308\\dep-lib-unicode_segmentation", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 6466336266548934813, "compile_kind": 14682669768258224367}