{"rustc": 7868289081541623310, "features": "[]", "declared_features": "[]", "target": 15145676655729463769, "profile": 12366199790041765268, "path": 15246182079818456646, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\debug\\.fingerprint\\tinyvec_macros-bbae3defa5473df3\\dep-lib-tinyvec_macros", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 6466336266548934813, "compile_kind": 14682669768258224367}