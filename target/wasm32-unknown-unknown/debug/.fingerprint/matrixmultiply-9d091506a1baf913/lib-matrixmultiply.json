{"rustc": 7868289081541623310, "features": "[\"default\", \"std\"]", "declared_features": "[\"cgemm\", \"constconf\", \"default\", \"num_cpus\", \"once_cell\", \"std\", \"thread-tree\", \"threading\"]", "target": 7055067433712553826, "profile": 12366199790041765268, "path": 13430345771230512115, "deps": [[15709748443193639506, "rawpointer", false, 17011589390961128219], [15826188163127377936, "build_script_build", false, 15171128332148617784]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\debug\\.fingerprint\\matrixmultiply-9d091506a1baf913\\dep-lib-matrixmultiply", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 6466336266548934813, "compile_kind": 14682669768258224367}