{"rustc": 7868289081541623310, "features": "[\"block-padding\"]", "declared_features": "[\"block-padding\", \"std\"]", "target": 16139718221464202370, "profile": 12366199790041765268, "path": 12334406014880107745, "deps": [[10520923840501062997, "generic_array", false, 15551253550113265615], [13624526718496097675, "block_padding", false, 8520433812491593073]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\debug\\.fingerprint\\inout-4b88e6fa183d3692\\dep-lib-inout", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 6466336266548934813, "compile_kind": 14682669768258224367}