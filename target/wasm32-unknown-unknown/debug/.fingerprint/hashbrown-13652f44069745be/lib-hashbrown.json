{"rustc": 7868289081541623310, "features": "[\"allocator-api2\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"raw-entry\"]", "declared_features": "[\"alloc\", \"allocator-api2\", \"core\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"nightly\", \"raw-entry\", \"rayon\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 13796197676120832388, "profile": 12366199790041765268, "path": 11499000141761425681, "deps": [[5230392855116717286, "equivalent", false, 2453656107308706711], [9150530836556604396, "allocator_api2", false, 2684897053392355253], [10842263908529601448, "<PERSON><PERSON><PERSON>", false, 17981065035848083846]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\debug\\.fingerprint\\hashbrown-13652f44069745be\\dep-lib-hashbrown", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 6466336266548934813, "compile_kind": 14682669768258224367}