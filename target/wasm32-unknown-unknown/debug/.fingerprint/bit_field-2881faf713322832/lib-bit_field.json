{"rustc": 7868289081541623310, "features": "[]", "declared_features": "[]", "target": 8753832435097325874, "profile": 12366199790041765268, "path": 15027250490408947595, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\debug\\.fingerprint\\bit_field-2881faf713322832\\dep-lib-bit_field", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 6466336266548934813, "compile_kind": 14682669768258224367}