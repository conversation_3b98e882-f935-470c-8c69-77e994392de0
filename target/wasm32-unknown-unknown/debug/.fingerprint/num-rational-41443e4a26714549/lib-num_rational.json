{"rustc": 7868289081541623310, "features": "[]", "declared_features": "[\"default\", \"num-bigint\", \"num-bigint-std\", \"serde\", \"std\"]", "target": 10895754937005166100, "profile": 12366199790041765268, "path": 9039666191551663924, "deps": [[5157631553186200874, "num_traits", false, 9785100474583394429], [16795989132585092538, "num_integer", false, 8344290930509533615]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\debug\\.fingerprint\\num-rational-41443e4a26714549\\dep-lib-num_rational", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 6466336266548934813, "compile_kind": 14682669768258224367}