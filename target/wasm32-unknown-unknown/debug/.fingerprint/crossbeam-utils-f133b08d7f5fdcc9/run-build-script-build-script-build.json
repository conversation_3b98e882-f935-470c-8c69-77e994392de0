{"rustc": 7868289081541623310, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[4468123440088164316, "build_script_build", false, 17253366123026170095]], "local": [{"RerunIfChanged": {"output": "wasm32-unknown-unknown\\debug\\build\\crossbeam-utils-f133b08d7f5fdcc9\\output", "paths": ["no_atomic.rs"]}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 0, "compile_kind": 0}