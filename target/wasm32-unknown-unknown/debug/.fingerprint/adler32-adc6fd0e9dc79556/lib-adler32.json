{"rustc": 7868289081541623310, "features": "[\"default\", \"std\"]", "declared_features": "[\"compiler_builtins\", \"core\", \"default\", \"rustc-dep-of-std\", \"std\"]", "target": 340870475748378612, "profile": 12366199790041765268, "path": 10978592568007297832, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\debug\\.fingerprint\\adler32-adc6fd0e9dc79556\\dep-lib-adler32", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 6466336266548934813, "compile_kind": 14682669768258224367}