{"rustc": 7868289081541623310, "features": "[]", "declared_features": "[]", "target": 4590690216173873293, "profile": 12366199790041765268, "path": 17837689823852729958, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\debug\\.fingerprint\\lebe-5c95e18b939b9625\\dep-lib-lebe", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 6466336266548934813, "compile_kind": 14682669768258224367}