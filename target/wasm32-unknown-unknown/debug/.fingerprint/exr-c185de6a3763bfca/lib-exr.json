{"rustc": 7868289081541623310, "features": "[]", "declared_features": "[]", "target": 7534689274471719961, "profile": 12366199790041765268, "path": 1910266973248939315, "deps": [[2328992793207497738, "bit_field", false, 8604281170894839480], [3666196340704888985, "smallvec", false, 18199778954549508996], [3746573929696391749, "rayon_core", false, 12908403405751109622], [5311759941895549171, "lebe", false, 18086037789798767169], [7636735136738807108, "miniz_oxide", false, 5846981704913515250], [11952083740819019228, "zune_inflate", false, 10389758122730957398], [16857843618210199216, "half", false, 5132432043979788673]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\debug\\.fingerprint\\exr-c185de6a3763bfca\\dep-lib-exr", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 6466336266548934813, "compile_kind": 14682669768258224367}