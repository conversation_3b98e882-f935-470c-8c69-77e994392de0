{"rustc": 7868289081541623310, "features": "[\"alloc\", \"approx\", \"default\", \"named\", \"named_from_str\", \"phf\", \"std\"]", "declared_features": "[\"alloc\", \"approx\", \"bytemuck\", \"default\", \"find-crate\", \"libm\", \"named\", \"named_from_str\", \"phf\", \"rand\", \"random\", \"serde\", \"serializing\", \"std\", \"wide\"]", "target": 6519571351683251528, "profile": 12366199790041765268, "path": 1042659591359196314, "deps": [[3333774257163257407, "fast_srgb8", false, 18186986071194605596], [5055421393008045653, "palette_derive", false, 10016780267075895154], [12385318719625307482, "build_script_main", false, 9262698896459192290], [15677050387741058262, "approx", false, 16426070772779142081], [17186037756130803222, "phf", false, 1651650012336753298]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\debug\\.fingerprint\\palette-84313261851aeeb8\\dep-lib-palette", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 6466336266548934813, "compile_kind": 14682669768258224367}