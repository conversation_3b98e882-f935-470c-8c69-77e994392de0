{"rustc": 7868289081541623310, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[5157631553186200874, "build_script_build", false, 418568772537611291]], "local": [{"RerunIfChanged": {"output": "wasm32-unknown-unknown\\debug\\build\\num-traits-332e4f862234398a\\output", "paths": ["build.rs"]}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 0, "compile_kind": 0}