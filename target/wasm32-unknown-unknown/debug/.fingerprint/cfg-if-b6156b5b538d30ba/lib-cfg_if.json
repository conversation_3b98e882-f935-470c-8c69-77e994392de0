{"rustc": 7868289081541623310, "features": "[]", "declared_features": "[\"core\", \"rustc-dep-of-std\"]", "target": 13840298032947503755, "profile": 12366199790041765268, "path": 5142679350089520778, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\debug\\.fingerprint\\cfg-if-b6156b5b538d30ba\\dep-lib-cfg_if", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 6466336266548934813, "compile_kind": 14682669768258224367}