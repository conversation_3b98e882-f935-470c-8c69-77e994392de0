{"rustc": 7868289081541623310, "features": "[]", "declared_features": "[]", "target": 8958406094080315647, "profile": 17565198560131644146, "path": 13968130968443794016, "deps": [[7826122624549889939, "build_script_build", false, 5099425155420782086], [10637008577242657367, "unicode_ident", false, 7076271416067236846]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\debug\\.fingerprint\\wasm-bindgen-shared-25c9b7a2251b5c20\\dep-lib-wasm_bindgen_shared", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 6466336266548934813, "compile_kind": 14682669768258224367}