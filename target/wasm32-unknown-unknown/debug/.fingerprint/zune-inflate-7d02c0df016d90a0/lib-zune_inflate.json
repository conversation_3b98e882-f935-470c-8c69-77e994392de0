{"rustc": 7868289081541623310, "features": "[\"simd-adler32\", \"zlib\"]", "declared_features": "[\"default\", \"gzip\", \"simd-adler32\", \"std\", \"zlib\"]", "target": 12020662131698132232, "profile": 12366199790041765268, "path": 1867639236521283962, "deps": [[4018467389006652250, "simd_adler32", false, 17552133868866341548]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\debug\\.fingerprint\\zune-inflate-7d02c0df016d90a0\\dep-lib-zune_inflate", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 6466336266548934813, "compile_kind": 14682669768258224367}