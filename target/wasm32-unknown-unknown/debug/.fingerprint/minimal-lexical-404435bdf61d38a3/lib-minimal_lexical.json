{"rustc": 7868289081541623310, "features": "[\"std\"]", "declared_features": "[\"alloc\", \"compact\", \"default\", \"lint\", \"nightly\", \"std\"]", "target": 10619533105316148159, "profile": 12366199790041765268, "path": 1408478498877045981, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\debug\\.fingerprint\\minimal-lexical-404435bdf61d38a3\\dep-lib-minimal_lexical", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory", "-C", "target-feature=+mutable-globals"], "config": 6466336266548934813, "compile_kind": 14682669768258224367}