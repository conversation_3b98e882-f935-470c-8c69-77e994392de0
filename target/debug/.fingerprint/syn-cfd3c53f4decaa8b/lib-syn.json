{"rustc": 7868289081541623310, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"visit\", \"visit-mut\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 15657897354478470176, "path": 6979928755204208704, "deps": [[373107762698212489, "proc_macro2", false, 17859279283667887749], [10637008577242657367, "unicode_ident", false, 11335439194656267934], [17990358020177143287, "quote", false, 3844745559053346607]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\syn-cfd3c53f4decaa8b\\dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}