{"rustc": 7868289081541623310, "features": "[]", "declared_features": "[\"strict-macro\"]", "target": 6875603382767429092, "profile": 16022134717719021851, "path": 13183774904344741006, "deps": [[4190627925692779123, "wasm_bindgen_macro_support", false, 10157528842331027502], [17990358020177143287, "quote", false, 3844745559053346607]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\wasm-bindgen-macro-5ea1ea35ef540068\\dep-lib-wasm_bindgen_macro", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}