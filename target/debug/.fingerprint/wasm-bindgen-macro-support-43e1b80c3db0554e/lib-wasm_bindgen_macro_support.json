{"rustc": 7868289081541623310, "features": "[]", "declared_features": "[\"extra-traits\", \"strict-macro\"]", "target": 17930477452216118438, "profile": 16022134717719021851, "path": 15322381914801151561, "deps": [[373107762698212489, "proc_macro2", false, 17859279283667887749], [7826122624549889939, "wasm_bindgen_shared", false, 12387802390528233571], [10859219585559253335, "wasm_bindgen_backend", false, 2356742545623520], [17332570067994900305, "syn", false, 11526570330580793431], [17990358020177143287, "quote", false, 3844745559053346607]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\wasm-bindgen-macro-support-43e1b80c3db0554e\\dep-lib-wasm_bindgen_macro_support", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}